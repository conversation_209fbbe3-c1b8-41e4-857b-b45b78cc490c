-- CRITICAL FIX: Commission Conversion Pending Shares Issue
-- Run this SQL in Supabase Dashboard > SQL Editor
-- 
-- Problem: Commission-to-shares conversions show as "Pending Shares" instead of active shares
-- Solution: Update existing commission conversion purchases from 'pending' to 'active' status

-- First, let's see what we're dealing with
SELECT 
    'BEFORE FIX - Current State' as status,
    payment_method,
    status as purchase_status,
    COUNT(*) as count,
    SUM(shares_purchased) as total_shares
FROM aureus_share_purchases 
WHERE payment_method = 'Commission Conversion'
GROUP BY payment_method, status
ORDER BY status;

-- Show specific pending commission conversion shares
SELECT 
    'PENDING COMMISSION CONVERSIONS TO FIX' as info,
    id,
    user_id,
    shares_purchased,
    total_amount,
    status,
    created_at
FROM aureus_share_purchases 
WHERE payment_method = 'Commission Conversion' 
AND status = 'pending'
ORDER BY created_at DESC;

-- THE FIX: Update pending commission conversion shares to active status
UPDATE aureus_share_purchases 
SET 
    status = 'active',
    updated_at = NOW()
WHERE payment_method = 'Commission Conversion' 
AND status = 'pending';

-- Verify the fix worked
SELECT 
    'AFTER FIX - Updated State' as status,
    payment_method,
    status as purchase_status,
    COUNT(*) as count,
    SUM(shares_purchased) as total_shares
FROM aureus_share_purchases 
WHERE payment_method = 'Commission Conversion'
GROUP BY payment_method, status
ORDER BY status;

-- Show all commission conversion shares (should all be active now)
SELECT 
    'ALL COMMISSION CONVERSIONS AFTER FIX' as info,
    id,
    user_id,
    shares_purchased,
    total_amount,
    status,
    created_at
FROM aureus_share_purchases 
WHERE payment_method = 'Commission Conversion'
ORDER BY created_at DESC;

-- Summary of the fix
SELECT 
    'FIX SUMMARY' as summary,
    'Commission conversion shares updated from pending to active' as description,
    'Users will no longer see Pending Shares for processed conversions' as result;
