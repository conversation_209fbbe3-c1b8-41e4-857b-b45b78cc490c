const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('🔒 Initializing RLS-compliant Supabase client for Telegram bot...');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('❌ Missing Supabase environment variables. Please check your .env file.');
}

// Create service role client for admin operations only
const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create anon client for RLS-compliant operations
const anonClient = createClient(supabaseUrl, supabaseAnonKey || supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('✅ RLS-compliant Supabase clients initialized');

/**
 * RLS-Compliant Database Client for Telegram Bot
 * This client respects Row Level Security policies and ensures proper user isolation
 */
class RLSCompliantDB {
  constructor() {
    this.serviceClient = serviceClient; // For admin operations only
    this.anonClient = anonClient; // For user operations with RLS
    this.currentUserContext = null;
  }

  /**
   * Set user context for RLS operations
   * This simulates authentication for Telegram users
   */
  async setUserContext(telegramId, userId = null) {
    try {
      // If we have a user ID, try to get their auth_user_id
      if (userId) {
        const { data: user, error } = await this.serviceClient
          .from('users')
          .select('auth_user_id, id, username')
          .eq('id', userId)
          .single();

        if (!error && user && user.auth_user_id) {
          // User has proper auth_user_id, use it for RLS context
          this.currentUserContext = {
            telegramId,
            userId: user.id,
            authUserId: user.auth_user_id,
            username: user.username,
            hasAuth: true
          };
          
          console.log(`🔒 [RLS] Set user context: Telegram ${telegramId} -> User ${userId} (Auth: ${user.auth_user_id})`);
          return true;
        }
      }

      // Fallback: User doesn't have auth_user_id (Telegram-only user)
      this.currentUserContext = {
        telegramId,
        userId,
        authUserId: null,
        hasAuth: false
      };
      
      console.log(`⚠️ [RLS] Set limited context: Telegram ${telegramId} -> User ${userId} (No Auth)`);
      return false;
    } catch (error) {
      console.error('❌ [RLS] Error setting user context:', error);
      this.currentUserContext = null;
      return false;
    }
  }

  /**
   * Clear user context
   */
  clearUserContext() {
    this.currentUserContext = null;
    console.log('🔒 [RLS] Cleared user context');
  }

  /**
   * Get appropriate client based on operation type and user context
   */
  getClient(operationType = 'user') {
    if (operationType === 'admin' || operationType === 'system') {
      // Admin operations use service role (bypasses RLS)
      return this.serviceClient;
    }

    if (this.currentUserContext && this.currentUserContext.hasAuth) {
      // User has proper auth, use authenticated client
      // TODO: Implement proper JWT token for user
      return this.anonClient;
    }

    // For Telegram-only users, use service client with manual RLS checks
    return this.serviceClient;
  }

  /**
   * Validate user access for RLS compliance
   */
  validateUserAccess(targetUserId, operation = 'read') {
    if (!this.currentUserContext) {
      console.error('❌ [RLS] No user context set for operation');
      return false;
    }

    // Allow access if no specific user ID is being targeted (general queries)
    if (!targetUserId) {
      return true;
    }

    if (this.currentUserContext.userId !== targetUserId) {
      console.error(`❌ [RLS] Access denied: User ${this.currentUserContext.userId} cannot ${operation} data for User ${targetUserId}`);
      return false;
    }

    return true;
  }

  // ==========================================
  // RLS-COMPLIANT USER OPERATIONS
  // ==========================================

  async getTelegramUser(telegramId) {
    try {
      // Validate input
      if (!telegramId || telegramId === 'null' || telegramId === null || telegramId === undefined) {
        console.error('❌ [RLS] Invalid telegram_id provided to getTelegramUser:', telegramId);
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS] Error getting telegram user:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS] Error getting telegram user:', error);
      return null;
    }
  }

  async createTelegramUser(telegramId, userData) {
    try {
      // For new user registration, we need to allow telegram user creation
      // even if the context doesn't match yet (context will be set after creation)
      console.log(`🔗 [RLS] Creating telegram user mapping: ${telegramId} -> User ID ${userData.user_id}`);

      // Skip RLS validation for telegram user creation during registration
      // This is safe because we're creating the initial mapping

      const { data, error } = await this.serviceClient
        .from('telegram_users')
        .insert([{
          telegram_id: telegramId,
          user_id: userData.user_id,
          username: userData.username,
          first_name: userData.first_name,
          last_name: userData.last_name,
          is_registered: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();
      
      if (error) {
        console.error('❌ [RLS] Error creating telegram user:', error);
        return null;
      }
      
      console.log(`✅ [RLS] Created telegram user mapping: ${telegramId} -> User ID ${userData.user_id}`);
      return data;
    } catch (error) {
      console.error('❌ [RLS] Error creating telegram user:', error);
      return null;
    }
  }

  async createUser(userData) {
    try {
      // For user creation, we need to use service role but ensure proper data
      const { data, error } = await this.serviceClient
        .from('users')
        .insert([{
          ...userData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();
      
      if (error) {
        console.error('❌ [RLS] Error creating user:', error);
        return null;
      }
      
      console.log(`✅ [RLS] Created user: ${data.username} (ID: ${data.id})`);
      return data;
    } catch (error) {
      console.error('❌ [RLS] Error creating user:', error);
      return null;
    }
  }

  async getUserById(userId) {
    try {
      // During authentication, we may not have proper user context yet
      // Use service client for user lookups during auth flow
      const { data, error } = await this.serviceClient
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS] Error getting user by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS] Error getting user by ID:', error);
      return null;
    }
  }

  async getUserByUsername(username) {
    try {
      const { data, error } = await this.serviceClient
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS] Error getting user by username:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS] Error getting user by username:', error);
      return null;
    }
  }

  // ==========================================
  // ADMIN OPERATIONS (Service Role)
  // ==========================================

  async adminOperation(operation) {
    console.log('🔧 [RLS] Executing admin operation with service role');
    return await operation(this.serviceClient);
  }

  // ==========================================
  // AUTH USER CREATION FOR TELEGRAM USERS
  // ==========================================

  async createAuthUserForTelegramUser(userId, telegramId, userData) {
    // 🚨 TEMPORARY DISABLE: Auth user creation is failing consistently
    // Users can function without auth_user_id, so disable this for now
    const ENABLE_AUTH_USER_CREATION = process.env.ENABLE_AUTH_USER_CREATION === 'true';

    if (!ENABLE_AUTH_USER_CREATION) {
      console.log(`⚠️ [RLS] Auth user creation disabled for Telegram user ${telegramId} -> User ${userId}`);
      console.log(`⚠️ [RLS] User ${userId} will function without auth_user_id (service role access)`);
      return null;
    }

    try {
      console.log(`🔐 [RLS] Creating auth user for Telegram user ${telegramId} -> User ${userId}`);

      // Check if auth user already exists with this email
      const authEmail = `telegram_${telegramId}@aureus.local`;

      try {
        // Try to get existing auth user first
        const { data: existingAuthUsers, error: listError } = await this.serviceClient.auth.admin.listUsers();

        if (!listError && existingAuthUsers && existingAuthUsers.users) {
          const existingAuthUser = existingAuthUsers.users.find(user => user.email === authEmail);

          if (existingAuthUser) {
            console.log(`🔐 [RLS] Found existing auth user for email ${authEmail}: ${existingAuthUser.id}`);

            // Update users table with existing auth_user_id
            const { error: updateError } = await this.serviceClient
              .from('users')
              .update({
                auth_user_id: existingAuthUser.id,
                updated_at: new Date().toISOString()
              })
              .eq('id', userId);

            if (updateError) {
              console.error('❌ [RLS] Error updating user with existing auth_user_id:', updateError);
            } else {
              console.log(`✅ [RLS] Linked existing auth user ${existingAuthUser.id} to User ${userId}`);
            }

            return existingAuthUser;
          }
        }
      } catch (listError) {
        console.log(`⚠️ [RLS] Could not check for existing auth users:`, listError);
      }

      // Create new Supabase auth user
      const { data: authUser, error: authError } = await this.serviceClient.auth.admin.createUser({
        email: authEmail,
        password: this.generateSecurePassword(),
        email_confirm: true,
        user_metadata: {
          telegram_id: telegramId,
          source: 'telegram_bot',
          username: userData.username,
          full_name: userData.full_name || userData.username
        }
      });

      if (authError) {
        console.error('❌ [RLS] Error creating auth user:', authError);

        // If creation failed due to existing email, try to find and link the existing user
        if (authError.message && authError.message.includes('already registered')) {
          console.log(`⚠️ [RLS] Auth user already exists for ${authEmail}, skipping creation`);
          return null; // User can still function without auth_user_id
        }

        return null;
      }

      // Update users table with auth_user_id
      const { error: updateError } = await this.serviceClient
        .from('users')
        .update({
          auth_user_id: authUser.user.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        console.error('❌ [RLS] Error updating user with auth_user_id:', updateError);
        return null;
      }

      console.log(`✅ [RLS] Created auth user ${authUser.user.id} for User ${userId}`);
      return authUser.user;
    } catch (error) {
      console.error('❌ [RLS] Error creating auth user for Telegram user:', error);
      return null;
    }
  }

  generateSecurePassword() {
    return Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12);
  }

  // ==========================================
  // RLS-COMPLIANT OPERATIONS
  // ==========================================

  async getCommissionBalance(userId) {
    try {
      if (!this.validateUserAccess(userId, 'read')) {
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS] Error getting commission balance:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS] Error getting commission balance:', error);
      return null;
    }
  }

  async createPaymentTransaction(userId, transactionData) {
    try {
      if (!this.validateUserAccess(userId, 'create')) {
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('crypto_payment_transactions')
        .insert([{
          ...transactionData,
          user_id: userId,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ [RLS] Error creating payment transaction:', error);
        return null;
      }

      console.log(`✅ [RLS] Created payment transaction for User ${userId}`);
      return data;
    } catch (error) {
      console.error('❌ [RLS] Error creating payment transaction:', error);
      return null;
    }
  }

  async createKYCSubmission(userId, kycData) {
    try {
      if (!this.validateUserAccess(userId, 'create')) {
        return null;
      }

      // For KYC, we need authenticated role - ensure user has auth_user_id
      const user = await this.getUserById(userId);
      if (!user || !user.auth_user_id) {
        console.error('❌ [RLS] User needs auth_user_id for KYC submission');
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('kyc_information')
        .insert([{
          ...kycData,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ [RLS] Error creating KYC submission:', error);
        return null;
      }

      console.log(`✅ [RLS] Created KYC submission for User ${userId}`);
      return data;
    } catch (error) {
      console.error('❌ [RLS] Error creating KYC submission:', error);
      return null;
    }
  }

  // ==========================================
  // RLS-COMPLIANT COMMISSION OPERATIONS
  // ==========================================

  async getCommissionBalanceRLS(userId) {
    try {
      if (!this.validateUserAccess(userId, 'read')) {
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS] Error getting commission balance:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS] Error getting commission balance:', error);
      return null;
    }
  }

  async updateCommissionBalance(userId, updateData) {
    try {
      if (!this.validateUserAccess(userId, 'update')) {
        return null;
      }

      const { data, error } = await this.serviceClient
        .from('commission_balances')
        .upsert({
          user_id: userId,
          ...updateData,
          last_updated: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ [RLS] Error updating commission balance:', error);
        return null;
      }

      console.log(`✅ [RLS] Updated commission balance for User ${userId}`);
      return data;
    } catch (error) {
      console.error('❌ [RLS] Error updating commission balance:', error);
      return null;
    }
  }

  // ==========================================
  // ADMIN OPERATIONS WITH PROPER VALIDATION
  // ==========================================

  async adminGetCommissionBalance(userId) {
    try {
      console.log(`🔧 [RLS-ADMIN] Admin accessing commission balance for User ${userId}`);

      const { data, error } = await this.serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ [RLS-ADMIN] Error getting commission balance:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ [RLS-ADMIN] Error getting commission balance:', error);
      return null;
    }
  }

  async adminUpdateCommissionBalance(userId, updateData) {
    try {
      console.log(`🔧 [RLS-ADMIN] Admin updating commission balance for User ${userId}`);

      const { data, error } = await this.serviceClient
        .from('commission_balances')
        .upsert({
          user_id: userId,
          ...updateData,
          last_updated: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ [RLS-ADMIN] Error updating commission balance:', error);
        return null;
      }

      console.log(`✅ [RLS-ADMIN] Updated commission balance for User ${userId}`);
      return data;
    } catch (error) {
      console.error('❌ [RLS-ADMIN] Error updating commission balance:', error);
      return null;
    }
  }

  // ==========================================
  // LEGACY COMPATIBILITY
  // ==========================================

  get client() {
    // Return service client for backward compatibility
    // TODO: Gradually migrate all operations to use RLS-compliant methods
    return this.serviceClient;
  }
}

// Export singleton instance
const rlsDb = new RLSCompliantDB();

module.exports = {
  serviceClient,
  anonClient,
  rlsDb,
  RLSCompliantDB
};
