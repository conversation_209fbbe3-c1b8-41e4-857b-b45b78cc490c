// Referral Performance Analytics Implementation
// Provides detailed performance metrics and analytics for referral system
// Date: 2025-01-10

const { db } = require('../src/database/supabase-client');

/**
 * Handle performance analytics view
 */
async function handlePerformanceAnalytics(ctx) {
  const user = ctx.from;

  try {
    console.log(`📊 [ANALYTICS] Loading performance analytics for user ${user.id} (@${user.username})`);

    // Get user ID
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', user.id)
      .single();

    if (telegramError || !telegramUser) {
      await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
      return;
    }

    // Get performance data for different time periods
    const periods = [
      { name: '7 Days', days: 7 },
      { name: '30 Days', days: 30 },
      { name: '90 Days', days: 90 }
    ];

    let analyticsMessage = `📊 **REFERRAL PERFORMANCE ANALYTICS**\n\n`;

    for (const period of periods) {
      const startDate = new Date(Date.now() - period.days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const endDate = new Date().toISOString().split('T')[0];

      const { data: periodData, error: periodError } = await db.client
        .rpc('get_referral_performance_summary', {
          p_referrer_id: telegramUser.user_id,
          p_start_date: startDate,
          p_end_date: endDate
        });

      if (!periodError && periodData && periodData.length > 0) {
        const data = periodData[0];
        
        analyticsMessage += `📈 **${period.name.toUpperCase()} PERFORMANCE**\n`;
        analyticsMessage += `• **New Referrals:** ${data.new_referrals_period}\n`;
        analyticsMessage += `• **Total Commission:** $${(parseFloat(data.total_usdt_commission) + parseFloat(data.total_share_commission)).toFixed(2)}\n`;
        analyticsMessage += `• **USDT Commission:** $${parseFloat(data.total_usdt_commission).toFixed(2)}\n`;
        analyticsMessage += `• **Share Commission:** ${parseFloat(data.total_share_commission).toFixed(2)} shares\n`;
        analyticsMessage += `• **Avg per Referral:** $${parseFloat(data.avg_commission_per_referral).toFixed(2)}\n`;
        
        if (data.commission_growth_rate !== null) {
          const growthIcon = data.commission_growth_rate >= 0 ? '📈' : '📉';
          analyticsMessage += `• **Growth Rate:** ${growthIcon} ${parseFloat(data.commission_growth_rate).toFixed(1)}%\n`;
        }
        
        analyticsMessage += `\n`;
      }
    }

    // Get monthly breakdown for the last 6 months
    const monthlyData = await getMonthlyBreakdown(telegramUser.user_id);
    if (monthlyData.length > 0) {
      analyticsMessage += `📅 **MONTHLY BREAKDOWN (Last 6 Months)**\n`;
      monthlyData.forEach(month => {
        analyticsMessage += `• **${month.month}:** $${month.total_commission.toFixed(2)} (${month.transaction_count} transactions)\n`;
      });
      analyticsMessage += `\n`;
    }

    // Get referral conversion metrics
    const conversionMetrics = await getReferralConversionMetrics(telegramUser.user_id);
    if (conversionMetrics) {
      analyticsMessage += `🎯 **CONVERSION METRICS**\n`;
      analyticsMessage += `• **Total Referrals:** ${conversionMetrics.total_referrals}\n`;
      analyticsMessage += `• **Active Referrals:** ${conversionMetrics.active_referrals}\n`;
      analyticsMessage += `• **Purchasing Referrals:** ${conversionMetrics.purchasing_referrals}\n`;
      analyticsMessage += `• **Conversion Rate:** ${conversionMetrics.conversion_rate.toFixed(1)}%\n`;
      analyticsMessage += `• **Avg Time to First Purchase:** ${conversionMetrics.avg_time_to_purchase} days\n\n`;
    }

    // Get top performing referrals
    const topReferrals = await getTopPerformingReferrals(telegramUser.user_id, 5);
    if (topReferrals.length > 0) {
      analyticsMessage += `🏆 **TOP PERFORMING REFERRALS**\n`;
      topReferrals.forEach((referral, index) => {
        const safeUsername = (referral.username || 'Anonymous').replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
        analyticsMessage += `${index + 1}. **${safeUsername}**: $${referral.total_commission.toFixed(2)} (${referral.transaction_count} transactions)\n`;
      });
      analyticsMessage += `\n`;
    }

    // Performance insights and recommendations
    analyticsMessage += `💡 **PERFORMANCE INSIGHTS**\n`;
    const insights = generatePerformanceInsights(monthlyData, conversionMetrics);
    insights.forEach(insight => {
      analyticsMessage += `• ${insight}\n`;
    });

    const keyboard = [
      [
        { text: "📊 Detailed Breakdown", callback_data: "detailed_analytics_breakdown" },
        { text: "📈 Commission Trends", callback_data: "commission_trends" }
      ],
      [
        { text: "🎯 Optimization Tips", callback_data: "referral_optimization_tips" },
        { text: "📋 Export Analytics", callback_data: "export_analytics_report" }
      ],
      [
        { text: "🔄 Refresh Analytics", callback_data: "view_performance_analytics" },
        { text: "🔙 Back to Dashboard", callback_data: "enhanced_referral_dashboard" }
      ]
    ];

    try {
      await ctx.replyWithMarkdown(analyticsMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    } catch (markdownError) {
      console.error('❌ Markdown parsing error in performance analytics:', markdownError);
      
      // Fallback: Send without markdown formatting
      const plainMessage = analyticsMessage.replace(/\*\*/g, '').replace(/`/g, '');
      await ctx.reply(plainMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    }

  } catch (error) {
    console.error('Performance analytics error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading performance analytics**\n\nPlease try again.');
  }
}

/**
 * Get monthly commission breakdown
 */
async function getMonthlyBreakdown(userId) {
  try {
    const { data, error } = await db.client
      .from('commission_transactions')
      .select('usdt_commission, share_commission, payment_date')
      .eq('referrer_id', userId)
      .eq('status', 'approved')
      .gte('payment_date', new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString())
      .order('payment_date', { ascending: false });

    if (error || !data) return [];

    // Group by month
    const monthlyData = {};
    data.forEach(transaction => {
      const date = new Date(transaction.payment_date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthName = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthName,
          total_commission: 0,
          transaction_count: 0
        };
      }
      
      monthlyData[monthKey].total_commission += parseFloat(transaction.usdt_commission || 0) + parseFloat(transaction.share_commission || 0);
      monthlyData[monthKey].transaction_count += 1;
    });

    return Object.values(monthlyData).sort((a, b) => b.month.localeCompare(a.month));
  } catch (error) {
    console.error('Error getting monthly breakdown:', error);
    return [];
  }
}

/**
 * Get referral conversion metrics
 */
async function getReferralConversionMetrics(userId) {
  try {
    // Get total referrals
    const { data: referrals, error: referralsError } = await db.client
      .from('referrals')
      .select('id, referred_id, created_at, status')
      .eq('referrer_id', userId);

    if (referralsError || !referrals) return null;

    const totalReferrals = referrals.length;
    const activeReferrals = referrals.filter(r => r.status === 'active').length;

    // Get referrals who made purchases
    const { data: purchasingReferrals, error: purchasingError } = await db.client
      .from('commission_transactions')
      .select('referred_id')
      .eq('referrer_id', userId)
      .eq('status', 'approved');

    if (purchasingError) return null;

    const uniquePurchasingReferrals = [...new Set(purchasingReferrals.map(p => p.referred_id))].length;
    const conversionRate = totalReferrals > 0 ? (uniquePurchasingReferrals / totalReferrals) * 100 : 0;

    // Calculate average time to first purchase
    let avgTimeToPurchase = 0;
    if (purchasingReferrals.length > 0) {
      const { data: firstPurchases, error: firstPurchaseError } = await db.client
        .from('commission_transactions')
        .select('referred_id, payment_date')
        .eq('referrer_id', userId)
        .eq('status', 'approved')
        .order('payment_date', { ascending: true });

      if (!firstPurchaseError && firstPurchases) {
        const firstPurchasesByUser = {};
        firstPurchases.forEach(purchase => {
          if (!firstPurchasesByUser[purchase.referred_id]) {
            firstPurchasesByUser[purchase.referred_id] = purchase.payment_date;
          }
        });

        let totalDays = 0;
        let count = 0;
        
        Object.keys(firstPurchasesByUser).forEach(referredId => {
          const referral = referrals.find(r => r.referred_id == referredId);
          if (referral) {
            const joinDate = new Date(referral.created_at);
            const firstPurchaseDate = new Date(firstPurchasesByUser[referredId]);
            const daysDiff = Math.floor((firstPurchaseDate - joinDate) / (1000 * 60 * 60 * 24));
            totalDays += daysDiff;
            count += 1;
          }
        });

        avgTimeToPurchase = count > 0 ? Math.round(totalDays / count) : 0;
      }
    }

    return {
      total_referrals: totalReferrals,
      active_referrals: activeReferrals,
      purchasing_referrals: uniquePurchasingReferrals,
      conversion_rate: conversionRate,
      avg_time_to_purchase: avgTimeToPurchase
    };
  } catch (error) {
    console.error('Error getting conversion metrics:', error);
    return null;
  }
}

/**
 * Get top performing referrals
 */
async function getTopPerformingReferrals(userId, limit = 5) {
  try {
    const { data, error } = await db.client
      .from('commission_transactions')
      .select(`
        referred_id,
        usdt_commission,
        share_commission,
        users!commission_transactions_referred_id_fkey (
          username,
          full_name
        )
      `)
      .eq('referrer_id', userId)
      .eq('status', 'approved');

    if (error || !data) return [];

    // Group by referred user and calculate totals
    const referralTotals = {};
    data.forEach(transaction => {
      const referredId = transaction.referred_id;
      if (!referralTotals[referredId]) {
        referralTotals[referredId] = {
          username: transaction.users?.username || transaction.users?.full_name || 'Anonymous',
          total_commission: 0,
          transaction_count: 0
        };
      }
      
      referralTotals[referredId].total_commission += parseFloat(transaction.usdt_commission || 0) + parseFloat(transaction.share_commission || 0);
      referralTotals[referredId].transaction_count += 1;
    });

    return Object.values(referralTotals)
      .sort((a, b) => b.total_commission - a.total_commission)
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting top performing referrals:', error);
    return [];
  }
}

/**
 * Generate performance insights and recommendations
 */
function generatePerformanceInsights(monthlyData, conversionMetrics) {
  const insights = [];

  if (conversionMetrics) {
    if (conversionMetrics.conversion_rate < 20) {
      insights.push('🎯 Low conversion rate - consider improving your referral approach');
    } else if (conversionMetrics.conversion_rate > 50) {
      insights.push('🏆 Excellent conversion rate - your referral strategy is working well!');
    }

    if (conversionMetrics.avg_time_to_purchase > 30) {
      insights.push('⏰ Long time to first purchase - consider follow-up strategies');
    } else if (conversionMetrics.avg_time_to_purchase < 7) {
      insights.push('⚡ Quick conversion time - great referral quality!');
    }
  }

  if (monthlyData.length >= 2) {
    const latestMonth = monthlyData[0];
    const previousMonth = monthlyData[1];
    
    if (latestMonth.total_commission > previousMonth.total_commission) {
      insights.push('📈 Commission growth trend - keep up the great work!');
    } else if (latestMonth.total_commission < previousMonth.total_commission * 0.8) {
      insights.push('📉 Commission decline - consider re-engaging your network');
    }
  }

  if (insights.length === 0) {
    insights.push('📊 Keep building your referral network for better insights');
  }

  return insights;
}

/**
 * Handle individual referral details view
 */
async function handleIndividualReferralDetails(ctx) {
  const user = ctx.from;

  try {
    // Get user ID
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', user.id)
      .single();

    if (telegramError || !telegramUser) {
      await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
      return;
    }

    // Get detailed referral breakdown
    const { data: referralDetails, error: detailsError } = await db.client
      .rpc('get_referral_commission_breakdown', {
        p_referrer_id: telegramUser.user_id
      });

    if (detailsError || !referralDetails || referralDetails.length === 0) {
      await ctx.replyWithMarkdown('❌ **No referral details found**\n\nStart referring users to see detailed information.');
      return;
    }

    let detailsMessage = `🔍 **INDIVIDUAL REFERRAL DETAILS**\n\n`;
    detailsMessage += `Select a referral to view detailed commission history:\n\n`;

    const keyboard = [];
    referralDetails.slice(0, 20).forEach((referral, index) => {
      const totalCommission = parseFloat(referral.total_usdt_commission) + parseFloat(referral.total_share_commission);
      const safeUsername = (referral.referral_username || referral.referral_full_name || 'Anonymous')
        .replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
      
      detailsMessage += `**${index + 1}. ${safeUsername}**\n`;
      detailsMessage += `   💰 Total: $${totalCommission.toFixed(2)} | 🔄 Transactions: ${referral.commission_count}\n\n`;
      
      keyboard.push([{
        text: `📊 ${safeUsername} ($${totalCommission.toFixed(2)})`,
        callback_data: `view_referral_detail_${referral.referred_id}`
      }]);
    });

    keyboard.push([{ text: "🔙 Back to Dashboard", callback_data: "enhanced_referral_dashboard" }]);

    await ctx.replyWithMarkdown(detailsMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Individual referral details error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading referral details**\n\nPlease try again.');
  }
}

module.exports = {
  handlePerformanceAnalytics,
  handleIndividualReferralDetails,
  getMonthlyBreakdown,
  getReferralConversionMetrics,
  getTopPerformingReferrals,
  generatePerformanceInsights
};
