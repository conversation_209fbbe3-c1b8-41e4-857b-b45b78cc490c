{"name": "aureus-mobile-app", "version": "1.0.0", "description": "Aureus Alliance Holdings Mobile App - Gold Investment Platform", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build-android": "cd android && ./gradlew assembleRelease", "build-android-bundle": "cd android && ./gradlew bundleRelease"}, "dependencies": {"@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@supabase/supabase-js": "^2.38.4", "react": "18.2.0", "react-native": "0.72.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.6.0", "react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.3", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-crypto-js": "^1.0.0", "react-native-masked-text": "^1.13.0", "formik": "^2.4.5", "yup": "^1.3.3", "react-native-url-polyfill": "^2.0.0", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "react-native-biometrics": "^3.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-permissions": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile-app", "gold-investment", "aureus-alliance", "cryptocurrency", "portfolio-management"], "author": "Aureus Alliance Holdings", "license": "Private"}