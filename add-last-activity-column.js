const { createClient } = require('@supabase/supabase-js');

// Use environment variables or fallback to Railway environment
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.log('⚠️ [MIGRATION] Missing Supabase credentials, will try to continue...');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addLastActivityColumn() {
  try {
    console.log('🔧 [MIGRATION] Adding last_activity column to telegram_users...');
    
    // Add the missing column
    const { error: columnError } = await supabase.rpc('exec', {
      sql: `
        ALTER TABLE telegram_users 
        ADD COLUMN IF NOT EXISTS last_activity TIMESTAMPTZ DEFAULT NOW();
      `
    });
    
    if (columnError) {
      console.error('❌ [MIGRATION] Failed to add last_activity column:', columnError);
      return false;
    }
    
    console.log('✅ [MIGRATION] last_activity column added successfully!');
    
    // Add index for performance
    console.log('🔧 [MIGRATION] Adding index for last_activity...');
    
    const { error: indexError } = await supabase.rpc('exec', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_telegram_users_last_activity 
        ON telegram_users(last_activity);
      `
    });
    
    if (indexError) {
      console.log('⚠️ [MIGRATION] Index creation failed (non-critical):', indexError.message);
    } else {
      console.log('✅ [MIGRATION] Index created successfully!');
    }
    
    // Backfill existing records
    console.log('🔧 [MIGRATION] Backfilling existing records...');
    
    const { error: updateError } = await supabase.rpc('exec', {
      sql: `
        UPDATE telegram_users 
        SET last_activity = COALESCE(updated_at, created_at, NOW())
        WHERE last_activity IS NULL;
      `
    });
    
    if (updateError) {
      console.log('⚠️ [MIGRATION] Backfill failed (non-critical):', updateError.message);
    } else {
      console.log('✅ [MIGRATION] Existing records backfilled!');
    }
    
    // Test the column
    console.log('🧪 [MIGRATION] Testing last_activity column...');
    
    const { data: testData, error: testError } = await supabase
      .from('telegram_users')
      .select('telegram_id, last_activity')
      .limit(1);
    
    if (testError) {
      console.log('⚠️ [MIGRATION] Test query failed:', testError.message);
    } else {
      console.log('✅ [MIGRATION] Test query successful!');
      if (testData && testData.length > 0) {
        console.log('📊 [MIGRATION] Sample data:', testData[0]);
      }
    }
    
    console.log('🎉 [MIGRATION] last_activity column migration completed!');
    return true;
    
  } catch (error) {
    console.error('💥 [MIGRATION] Migration failed:', error);
    return false;
  }
}

// Run the migration
addLastActivityColumn()
  .then(success => {
    if (success) {
      console.log('✅ Migration completed successfully!');
      process.exit(0);
    } else {
      console.log('❌ Migration failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Migration crashed:', error);
    process.exit(1);
  });
