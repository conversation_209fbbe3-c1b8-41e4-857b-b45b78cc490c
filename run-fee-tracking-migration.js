// Simple migration runner for Railway deployment
// Adds fee tracking columns to commission_withdrawals table

const { db } = require('./src/database/supabase-client');
require('dotenv').config();

async function runFeeTrackingMigration() {
  try {
    console.log('🚀 [FEE-MIGRATION] Starting withdrawal fee tracking migration...');

    // Add fee tracking columns one by one with error handling
    const columns = [
      {
        name: 'transaction_fee',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS transaction_fee DECIMAL(10,2) DEFAULT 2.00;"
      },
      {
        name: 'net_amount', 
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS net_amount DECIMAL(10,2);"
      }
    ];

    for (const column of columns) {
      console.log(`📄 [FEE-MIGRATION] Adding column: ${column.name}`);
      
      const { error } = await db.client.rpc('exec', {
        sql: column.sql
      });

      if (error) {
        console.error(`❌ [FEE-MIGRATION] Failed to add ${column.name}:`, error);
      } else {
        console.log(`✅ [FEE-MIGRATION] Added column: ${column.name}`);
      }
    }

    // Update existing withdrawals to have fee data
    console.log('📊 [FEE-MIGRATION] Updating existing withdrawals with fee data...');
    
    const updateSql = `
      UPDATE commission_withdrawals 
      SET 
          transaction_fee = 2.00,
          net_amount = amount - 2.00
      WHERE transaction_fee IS NULL OR net_amount IS NULL;
    `;

    const { error: updateError } = await db.client.rpc('exec', {
      sql: updateSql
    });

    if (updateError) {
      console.error('❌ [FEE-MIGRATION] Failed to update existing withdrawals:', updateError);
    } else {
      console.log('✅ [FEE-MIGRATION] Updated existing withdrawals with fee data');
    }

    // Add indexes
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_transaction_fee ON commission_withdrawals(transaction_fee);",
      "CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_net_amount ON commission_withdrawals(net_amount);"
    ];

    for (const indexSql of indexes) {
      console.log(`📊 [FEE-MIGRATION] Creating index...`);
      
      const { error } = await db.client.rpc('exec', {
        sql: indexSql
      });

      if (error) {
        console.error(`❌ [FEE-MIGRATION] Index creation failed:`, error);
      } else {
        console.log(`✅ [FEE-MIGRATION] Index created successfully`);
      }
    }

    // Test the enhanced table structure
    console.log('🧪 [FEE-MIGRATION] Testing enhanced table structure...');
    
    const { data: testData, error: testError } = await db.client
      .from('commission_withdrawals')
      .select('id, amount, transaction_fee, net_amount')
      .limit(1);

    if (testError) {
      console.log('⚠️ [FEE-MIGRATION] Table test failed:', testError.message);
    } else {
      console.log('✅ [FEE-MIGRATION] Table structure test passed!');
      if (testData && testData.length > 0) {
        console.log('📊 [FEE-MIGRATION] Sample data:', testData[0]);
      }
    }

    console.log('🎉 [FEE-MIGRATION] Withdrawal fee tracking migration completed!');
    console.log('');
    console.log('✨ MIGRATION RESULTS:');
    console.log('• Added transaction_fee column (default $2.00)');
    console.log('• Added net_amount column (amount - fee)');
    console.log('• Updated existing withdrawals with fee data');
    console.log('• Created performance indexes');
    console.log('• Admin panel will now show fee breakdown');
    
    return true;

  } catch (error) {
    console.error('💥 [FEE-MIGRATION] Migration failed:', error);
    return false;
  }
}

// Run migration if called directly
if (require.main === module) {
  runFeeTrackingMigration()
    .then(success => {
      if (success) {
        console.log('🎯 [FEE-MIGRATION] Migration completed successfully!');
        process.exit(0);
      } else {
        console.log('❌ [FEE-MIGRATION] Migration failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 [FEE-MIGRATION] Migration error:', error);
      process.exit(1);
    });
}

module.exports = { runFeeTrackingMigration };
