const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function testPinAuth() {
  console.log('🧪 Testing PIN authentication system...');
  
  try {
    // Check for the latest PIN
    const { data: latestPin, error } = await supabase
      .from('auth_tokens')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (error) {
      console.log('❌ No PINs found:', error.message);
    } else {
      console.log('✅ Latest PIN found:');
      console.log('📊 Token:', latestPin.token);
      console.log('📊 Expires:', latestPin.expires_at);
      console.log('📊 Confirmed:', latestPin.confirmed);
      console.log('📊 Cancelled:', latestPin.cancelled);
      console.log('📊 Created:', latestPin.created_at);
      
      // Check if it's a 6-digit PIN
      if (/^\d{6}$/.test(latestPin.token)) {
        console.log('✅ Valid 6-digit PIN format');
        
        // Check if it's not expired
        const now = new Date();
        const expires = new Date(latestPin.expires_at);
        
        if (now < expires) {
          console.log('✅ PIN is still valid (not expired)');
          console.log(`⏰ Expires in ${Math.round((expires - now) / 1000 / 60)} minutes`);
        } else {
          console.log('❌ PIN has expired');
        }
      } else {
        console.log('❌ Not a 6-digit PIN format');
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

testPinAuth();
