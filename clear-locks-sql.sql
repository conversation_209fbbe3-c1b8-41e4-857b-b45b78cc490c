-- EMERGENCY: Clear all payment locks from telegram_users table
-- Run this SQL directly in Supabase Dashboard > SQL Editor

-- First, let's check what columns exist in the telegram_users table
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'telegram_users'
AND column_name LIKE '%payment%' OR column_name LIKE '%lock%' OR column_name LIKE '%progress%'
ORDER BY column_name;

-- Check for users with any lock-related fields set to true
SELECT
  COUNT(*) as total_users,
  COUNT(CASE WHEN payment_lock_status = true THEN 1 END) as payment_lock_status_true,
  COUNT(CASE WHEN payment_context IS NOT NULL THEN 1 END) as has_payment_context
FROM telegram_users;

-- Show details of locked users (using the correct column names)
SELECT
  telegram_id,
  username,
  payment_lock_status,
  payment_context,
  payment_lock_timestamp,
  created_at,
  updated_at
FROM telegram_users
WHERE payment_lock_status = true OR payment_context IS NOT NULL
ORDER BY payment_lock_timestamp DESC;

-- Clear all payment locks (using correct column names)
UPDATE telegram_users
SET
  payment_lock_status = false,
  payment_context = null,
  payment_lock_timestamp = null,
  updated_at = NOW()
WHERE payment_lock_status = true OR payment_context IS NOT NULL;

-- Verify all locks are cleared
SELECT
  COUNT(*) as remaining_locked_users,
  COUNT(CASE WHEN payment_lock_status = true THEN 1 END) as remaining_payment_locks,
  COUNT(CASE WHEN payment_context IS NOT NULL THEN 1 END) as remaining_payment_contexts
FROM telegram_users
WHERE payment_lock_status = true OR payment_context IS NOT NULL;

-- Show success message
SELECT 'All payment locks have been cleared successfully!' as status;
