/**
 * Aureus Alliance Holdings Mobile App
 * Commission Screen - Referral Earnings and Withdrawals
 */

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Share,
  Alert,
} from 'react-native';
import {Card, Button, Chip, Divider} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {useAuth} from '../../context/AuthContext';
import {colors, typography, spacing, shadows} from '../../constants/theme';

const CommissionScreen = ({navigation}) => {
  const {user} = useAuth();
  const {
    usdtBalance,
    shareCommissionBalance,
    commissions,
    refreshPortfolio,
  } = usePortfolio();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshPortfolio();
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const generateReferralLink = () => {
    const userId = user?.id || 'user123';
    return `https://aureus-alliance.com/register?ref=${userId}`;
  };

  const handleShareReferral = async () => {
    try {
      const referralLink = generateReferralLink();
      const message = `🌟 Join me on Aureus Alliance Holdings! 🌟

Invest in gold mining opportunities and earn passive income through our dual commission system:

💰 15% USDT Commission on referrals
🏆 15% Share Commission for long-term growth

Start your gold investment journey today:
${referralLink}

#GoldInvestment #PassiveIncome #AureusAlliance`;

      await Share.share({
        message,
        url: referralLink,
        title: 'Join Aureus Alliance Holdings',
      });
    } catch (error) {
      console.error('Error sharing referral link:', error);
    }
  };

  const handleWithdrawUSDT = () => {
    if (usdtBalance < 10) {
      Alert.alert(
        'Minimum Withdrawal',
        'Minimum withdrawal amount is $10 USDT.',
        [{text: 'OK'}]
      );
      return;
    }

    Alert.alert(
      'USDT Withdrawal',
      `Request withdrawal of ${formatCurrency(usdtBalance)}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Request Withdrawal',
          onPress: () => {
            // Navigate to withdrawal form or process withdrawal
            Alert.alert(
              'Withdrawal Requested',
              'Your withdrawal request has been submitted and will be processed within 24-48 hours.',
              [{text: 'OK'}]
            );
          },
        },
      ]
    );
  };

  const tabs = [
    {key: 'overview', label: 'Overview'},
    {key: 'history', label: 'History'},
    {key: 'referrals', label: 'Referrals'},
  ];

  const renderOverview = () => (
    <View style={styles.tabContent}>
      {/* Commission Balances */}
      <View style={styles.balanceCards}>
        <Card style={[styles.balanceCard, styles.usdtCard]}>
          <Card.Content>
            <View style={styles.balanceHeader}>
              <Icon name="account-balance" size={24} color={colors.usdt} />
              <Text style={styles.balanceTitle}>USDT Commission</Text>
            </View>
            <Text style={styles.balanceAmount}>{formatCurrency(usdtBalance)}</Text>
            <Button
              mode="contained"
              onPress={handleWithdrawUSDT}
              disabled={usdtBalance < 10}
              style={[styles.withdrawButton, {backgroundColor: colors.usdt}]}
              contentStyle={styles.withdrawButtonContent}
              labelStyle={styles.withdrawButtonLabel}>
              Withdraw USDT
            </Button>
          </Card.Content>
        </Card>

        <Card style={[styles.balanceCard, styles.shareCard]}>
          <Card.Content>
            <View style={styles.balanceHeader}>
              <Icon name="stars" size={24} color={colors.secondary} />
              <Text style={styles.balanceTitle}>Share Commission</Text>
            </View>
            <Text style={styles.balanceAmount}>{shareCommissionBalance.toFixed(2)}</Text>
            <Text style={styles.balanceSubtext}>
              Value: {formatCurrency(shareCommissionBalance * 5.00)}
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Referral Section */}
      <Card style={styles.referralCard}>
        <Card.Content>
          <View style={styles.referralHeader}>
            <Icon name="group-add" size={24} color={colors.primary} />
            <Text style={styles.referralTitle}>Invite Friends & Earn</Text>
          </View>
          <Text style={styles.referralDescription}>
            Earn 15% USDT + 15% Share commission on every referral's investment
          </Text>
          
          <View style={styles.referralStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>12</Text>
              <Text style={styles.statLabel}>Total Referrals</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>8</Text>
              <Text style={styles.statLabel}>Active This Month</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatCurrency(245.50)}</Text>
              <Text style={styles.statLabel}>Total Earned</Text>
            </View>
          </View>

          <Button
            mode="contained"
            onPress={handleShareReferral}
            style={styles.shareButton}
            contentStyle={styles.shareButtonContent}
            icon="share">
            Share Referral Link
          </Button>
        </Card.Content>
      </Card>

      {/* Commission Structure */}
      <Card style={styles.structureCard}>
        <Card.Content>
          <Text style={styles.structureTitle}>Commission Structure</Text>
          
          <View style={styles.structureItem}>
            <View style={styles.structureIcon}>
              <Icon name="account-balance" size={20} color={colors.usdt} />
            </View>
            <View style={styles.structureInfo}>
              <Text style={styles.structureName}>USDT Commission</Text>
              <Text style={styles.structureDescription}>
                15% of referral's investment paid in USDT
              </Text>
            </View>
            <Text style={styles.structureRate}>15%</Text>
          </View>

          <Divider style={styles.structureDivider} />

          <View style={styles.structureItem}>
            <View style={styles.structureIcon}>
              <Icon name="stars" size={20} color={colors.secondary} />
            </View>
            <View style={styles.structureInfo}>
              <Text style={styles.structureName}>Share Commission</Text>
              <Text style={styles.structureDescription}>
                15% of referral's shares added to your portfolio
              </Text>
            </View>
            <Text style={styles.structureRate}>15%</Text>
          </View>
        </Card.Content>
      </Card>
    </View>
  );

  const renderHistory = () => (
    <View style={styles.tabContent}>
      <Text style={styles.comingSoonText}>Commission history coming soon...</Text>
    </View>
  );

  const renderReferrals = () => (
    <View style={styles.tabContent}>
      <Text style={styles.comingSoonText}>Referral management coming soon...</Text>
    </View>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'overview':
        return renderOverview();
      case 'history':
        return renderHistory();
      case 'referrals':
        return renderReferrals();
      default:
        return renderOverview();
    }
  };

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabNavigation}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.tabChips}>
            {tabs.map((tab) => (
              <Chip
                key={tab.key}
                selected={selectedTab === tab.key}
                onPress={() => setSelectedTab(tab.key)}
                style={[
                  styles.tabChip,
                  selectedTab === tab.key && styles.selectedTabChip
                ]}
                textStyle={[
                  styles.tabChipText,
                  selectedTab === tab.key && styles.selectedTabChipText
                ]}>
                {tab.label}
              </Chip>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Tab Content */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }>
        {renderTabContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  tabNavigation: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabChips: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  tabChip: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedTabChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  tabChipText: {
    color: colors.textSecondary,
  },
  selectedTabChipText: {
    color: colors.surface,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  tabContent: {
    gap: spacing.lg,
  },
  balanceCards: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  balanceCard: {
    flex: 1,
    ...shadows.md,
  },
  usdtCard: {
    backgroundColor: colors.usdt + '10',
  },
  shareCard: {
    backgroundColor: colors.secondary + '10',
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  balanceTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  balanceAmount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  balanceSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  withdrawButton: {
    marginTop: spacing.sm,
  },
  withdrawButtonContent: {
    paddingVertical: 4,
  },
  withdrawButtonLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },
  referralCard: {
    ...shadows.md,
  },
  referralHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  referralTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  referralDescription: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: 22,
  },
  referralStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: colors.border,
    marginHorizontal: spacing.sm,
  },
  shareButton: {
    backgroundColor: colors.primary,
  },
  shareButtonContent: {
    paddingVertical: spacing.sm,
  },
  structureCard: {
    ...shadows.sm,
  },
  structureTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  structureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  structureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  structureInfo: {
    flex: 1,
  },
  structureName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: 4,
  },
  structureDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  structureRate: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  structureDivider: {
    marginVertical: spacing.md,
  },
  comingSoonText: {
    fontSize: typography.fontSize.lg,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xl,
  },
});

export default CommissionScreen;
