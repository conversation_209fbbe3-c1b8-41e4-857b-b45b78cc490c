/**
 * Aureus Alliance Holdings Mobile App
 * Authentication Context Provider
 * 
 * Manages user authentication state across the entire application
 */

import React, {createContext, useContext, useEffect, useReducer} from 'react';
import {authService, supabase} from '../services/supabaseClient';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initial state
const initialState = {
  user: null,
  session: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_SESSION: 'SET_SESSION',
  SET_ERROR: 'SET_ERROR',
  SIGN_OUT: 'SIGN_OUT',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer function
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.SET_SESSION:
      return {
        ...state,
        session: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.SIGN_OUT:
      return {
        ...initialState,
        isLoading: false,
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({children}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for existing session on app start
    checkAuthState();

    // Listen for auth state changes
    const {data: {subscription}} = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        if (session) {
          dispatch({type: AUTH_ACTIONS.SET_SESSION, payload: session});
          dispatch({type: AUTH_ACTIONS.SET_USER, payload: session.user});
          
          // Store user session info for offline access
          await AsyncStorage.setItem('user_session', JSON.stringify({
            userId: session.user.id,
            email: session.user.email,
            lastLogin: new Date().toISOString(),
          }));
        } else {
          dispatch({type: AUTH_ACTIONS.SIGN_OUT});
          await AsyncStorage.removeItem('user_session');
        }
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const checkAuthState = async () => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      
      const {session} = await authService.getSession();
      
      if (session) {
        dispatch({type: AUTH_ACTIONS.SET_SESSION, payload: session});
        dispatch({type: AUTH_ACTIONS.SET_USER, payload: session.user});
      } else {
        dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
    }
  };

  const signUp = async (email, password, userData = {}) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.signUp(email, password, userData);
      
      if (result.success) {
        // Note: User will need to verify email before being fully authenticated
        return {success: true, data: result.data};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign up failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const signIn = async (email, password) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.signIn(email, password);
      
      if (result.success) {
        // Auth state will be updated by the onAuthStateChange listener
        return {success: true, data: result.data};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign in failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const signOut = async () => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      
      const result = await authService.signOut();
      
      if (result.success) {
        // Auth state will be updated by the onAuthStateChange listener
        return {success: true};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign out failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const resetPassword = async (email) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.resetPassword(email);
      
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      
      if (result.success) {
        return {success: true};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Password reset failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const clearError = () => {
    dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});
  };

  // Context value
  const value = {
    // State
    user: state.user,
    session: state.session,
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    
    // Actions
    signUp,
    signIn,
    signOut,
    resetPassword,
    clearError,
    checkAuthState,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
