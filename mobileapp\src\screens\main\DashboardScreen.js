/**
 * Aureus Alliance Holdings Mobile App
 * Dashboard Screen - Main Overview (Replaces Telegram Bot Main Menu)
 */

import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Card, Button, Avatar, Badge} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, typography, spacing, shadows} from '../../constants/theme';

const {width} = Dimensions.get('window');

const DashboardScreen = ({navigation}) => {
  const {user} = useAuth();
  const {
    totalShares,
    totalValue,
    usdtBalance,
    shareCommissionBalance,
    isLoading,
    refreshPortfolio,
    getPortfolioSummary,
  } = usePortfolio();

  const [refreshing, setRefreshing] = useState(false);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    setGreeting(getGreeting());
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshPortfolio();
    setRefreshing(false);
  };

  const portfolioSummary = getPortfolioSummary();

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatShares = (shares) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(shares || 0);
  };

  const quickActions = [
    {
      title: 'Buy Shares',
      subtitle: 'Purchase gold shares',
      icon: 'shopping-cart',
      color: colors.success,
      onPress: () => navigation.navigate('Purchase'),
    },
    {
      title: 'Portfolio',
      subtitle: 'View investments',
      icon: 'account-balance-wallet',
      color: colors.primary,
      onPress: () => navigation.navigate('Portfolio'),
    },
    {
      title: 'Commissions',
      subtitle: 'Track earnings',
      icon: 'trending-up',
      color: colors.secondary,
      onPress: () => navigation.navigate('Commission'),
    },
    {
      title: 'Profile',
      subtitle: 'Account settings',
      icon: 'person',
      color: colors.textSecondary,
      onPress: () => navigation.navigate('Profile'),
    },
  ];

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContent}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]}
          tintColor={colors.primary}
        />
      }>
      
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Avatar.Text
            size={50}
            label={user?.user_metadata?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
            style={styles.avatar}
            labelStyle={styles.avatarLabel}
          />
          <View style={styles.greetingContainer}>
            <Text style={styles.greeting}>{greeting}!</Text>
            <Text style={styles.userName}>
              {user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Investor'}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity style={styles.notificationButton}>
          <Icon name="notifications" size={24} color={colors.surface} />
          <Badge style={styles.notificationBadge}>3</Badge>
        </TouchableOpacity>
      </View>

      {/* Portfolio Overview Card */}
      <Card style={styles.portfolioCard}>
        <Card.Content>
          <View style={styles.portfolioHeader}>
            <Text style={styles.portfolioTitle}>Portfolio Overview</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Portfolio')}>
              <Icon name="arrow-forward" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>

          <View style={styles.portfolioStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatShares(totalShares)}</Text>
              <Text style={styles.statLabel}>Total Shares</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatCurrency(portfolioSummary.currentValue)}</Text>
              <Text style={styles.statLabel}>Current Value</Text>
            </View>
          </View>

          <View style={styles.profitLossContainer}>
            <View style={styles.profitLossItem}>
              <Text style={styles.profitLossLabel}>Total Invested</Text>
              <Text style={styles.profitLossValue}>
                {formatCurrency(portfolioSummary.totalInvested)}
              </Text>
            </View>
            <View style={styles.profitLossItem}>
              <Text style={styles.profitLossLabel}>Profit/Loss</Text>
              <Text
                style={[
                  styles.profitLossValue,
                  {
                    color: portfolioSummary.profitLoss >= 0 ? colors.success : colors.error,
                  },
                ]}>
                {portfolioSummary.profitLoss >= 0 ? '+' : ''}
                {formatCurrency(portfolioSummary.profitLoss)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Commission Balances */}
      <View style={styles.commissionContainer}>
        <Card style={styles.commissionCard}>
          <Card.Content style={styles.commissionContent}>
            <View style={styles.commissionHeader}>
              <Icon name="account-balance" size={24} color={colors.usdt} />
              <Text style={styles.commissionTitle}>USDT Balance</Text>
            </View>
            <Text style={styles.commissionValue}>
              ${formatShares(usdtBalance)}
            </Text>
            <Text style={styles.commissionLabel}>Available for withdrawal</Text>
          </Card.Content>
        </Card>

        <Card style={styles.commissionCard}>
          <Card.Content style={styles.commissionContent}>
            <View style={styles.commissionHeader}>
              <Icon name="stars" size={24} color={colors.secondary} />
              <Text style={styles.commissionTitle}>Share Balance</Text>
            </View>
            <Text style={styles.commissionValue}>
              {formatShares(shareCommissionBalance)}
            </Text>
            <Text style={styles.commissionLabel}>Commission shares</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.quickActionItem}
              onPress={action.onPress}>
              <View style={[styles.quickActionIcon, {backgroundColor: action.color + '20'}]}>
                <Icon name={action.icon} size={28} color={action.color} />
              </View>
              <Text style={styles.quickActionTitle}>{action.title}</Text>
              <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Recent Activity */}
      <Card style={styles.activityCard}>
        <Card.Content>
          <View style={styles.activityHeader}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.activityList}>
            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, {backgroundColor: colors.success + '20'}]}>
                <Icon name="add" size={20} color={colors.success} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Share Purchase</Text>
                <Text style={styles.activitySubtitle}>Purchased 10.00 shares</Text>
                <Text style={styles.activityTime}>2 hours ago</Text>
              </View>
              <Text style={styles.activityAmount}>+$50.00</Text>
            </View>

            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, {backgroundColor: colors.secondary + '20'}]}>
                <Icon name="group" size={20} color={colors.secondary} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Referral Commission</Text>
                <Text style={styles.activitySubtitle}>From user referral</Text>
                <Text style={styles.activityTime}>1 day ago</Text>
              </View>
              <Text style={styles.activityAmount}>+$7.50</Text>
            </View>

            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, {backgroundColor: colors.info + '20'}]}>
                <Icon name="trending-up" size={20} color={colors.info} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Portfolio Update</Text>
                <Text style={styles.activitySubtitle}>Share value increased</Text>
                <Text style={styles.activityTime}>3 days ago</Text>
              </View>
              <Text style={styles.activityAmount}>+2.5%</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Market Info */}
      <Card style={styles.marketCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Market Information</Text>
          <View style={styles.marketInfo}>
            <View style={styles.marketItem}>
              <Text style={styles.marketLabel}>Current Share Price</Text>
              <Text style={styles.marketValue}>$5.00</Text>
            </View>
            <View style={styles.marketItem}>
              <Text style={styles.marketLabel}>Gold Price (oz)</Text>
              <Text style={styles.marketValue}>$2,045.30</Text>
            </View>
            <View style={styles.marketItem}>
              <Text style={styles.marketLabel}>Phase Status</Text>
              <Text style={[styles.marketValue, {color: colors.success}]}>Phase 1 Active</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.primary,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.secondary,
  },
  avatarLabel: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  greetingContainer: {
    marginLeft: spacing.md,
  },
  greeting: {
    fontSize: typography.fontSize.sm,
    color: colors.surface,
    opacity: 0.9,
  },
  userName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.surface,
  },
  notificationButton: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: colors.error,
  },
  portfolioCard: {
    margin: spacing.lg,
    marginBottom: spacing.md,
    ...shadows.md,
  },
  portfolioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  portfolioTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  portfolioStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.border,
  },
  profitLossContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  profitLossItem: {
    flex: 1,
  },
  profitLossLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  profitLossValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  commissionContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  commissionCard: {
    flex: 1,
    ...shadows.sm,
  },
  commissionContent: {
    alignItems: 'center',
  },
  commissionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  commissionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  commissionValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  commissionLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.textLight,
  },
  quickActionsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: (width - spacing.lg * 3) / 2,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  quickActionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  quickActionSubtitle: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  activityCard: {
    margin: spacing.lg,
    marginBottom: spacing.md,
    ...shadows.md,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  viewAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  activityList: {
    gap: spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  activitySubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  activityTime: {
    fontSize: typography.fontSize.xs,
    color: colors.textLight,
  },
  activityAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.success,
  },
  marketCard: {
    margin: spacing.lg,
    ...shadows.md,
  },
  marketInfo: {
    gap: spacing.md,
  },
  marketItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  marketLabel: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
  },
  marketValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
});

export default DashboardScreen;
