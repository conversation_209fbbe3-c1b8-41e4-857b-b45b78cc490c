/**
 * Aureus Alliance Holdings Mobile App
 * Supabase Client Configuration
 *
 * ⚠️  CRITICAL: CONNECTS TO LIVE PRODUCTION DATABASE
 * 💰 Contains real customer investments and financial data
 * 🔒 All operations affect live user accounts and real money
 *
 * This file configures the Supabase client for the mobile app,
 * maintaining compatibility with the existing Telegram bot production database.
 */

import {createClient} from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase configuration - SAME DATABASE AS TELEGRAM BOT
// CRITICAL: These values MUST match your main project .env file exactly
// The mobile app shares the same database as your Telegram bot

// For now using direct values - you should create a .env file with your actual credentials
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI';

// TODO: Replace with your actual Supabase anon key from the main project .env file
// You can find this in your main project's .env file under SUPABASE_ANON_KEY

// Create Supabase client with React Native specific configuration
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    // Use AsyncStorage for session persistence
    storage: AsyncStorage,
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session across app restarts
    persistSession: true,
    // Disable URL detection (not needed for mobile)
    detectSessionInUrl: false,
  },
  // Configure realtime subscriptions
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

/**
 * Authentication service functions
 */
export const authService = {
  /**
   * Sign up new user with email and password
   */
  async signUp(email, password, userData = {}) {
    try {
      const {data, error} = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName,
            phone: userData.phone,
            country: userData.country,
            source: 'mobile_app',
          },
        },
      });

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Sign up error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Sign in existing user
   */
  async signIn(email, password) {
    try {
      const {data, error} = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Sign in error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Sign out current user
   */
  async signOut() {
    try {
      const {error} = await supabase.auth.signOut();
      if (error) throw error;
      return {success: true};
    } catch (error) {
      console.error('Sign out error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Reset password
   */
  async resetPassword(email) {
    try {
      const {error} = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'aureus://reset-password',
      });

      if (error) throw error;
      return {success: true};
    } catch (error) {
      console.error('Reset password error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get current session
   */
  async getSession() {
    try {
      const {data: {session}, error} = await supabase.auth.getSession();
      if (error) throw error;
      return {success: true, session};
    } catch (error) {
      console.error('Get session error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get current user
   */
  async getCurrentUser() {
    try {
      const {data: {user}, error} = await supabase.auth.getUser();
      if (error) throw error;
      return {success: true, user};
    } catch (error) {
      console.error('Get current user error:', error);
      return {success: false, error: error.message};
    }
  },
};

/**
 * Database service functions
 */
export const dbService = {
  /**
   * Get user profile data - MATCHES TELEGRAM BOT SCHEMA
   */
  async getUserProfile(authUserId) {
    try {
      // First get the user from the users table using auth_user_id
      const {data: user, error: userError} = await supabase
        .from('users')
        .select(`
          id,
          username,
          email,
          full_name,
          phone,
          country,
          auth_user_id,
          is_active,
          created_at,
          updated_at,
          mobile_app_enabled,
          fcm_token,
          biometric_enabled
        `)
        .eq('auth_user_id', authUserId)
        .single();

      if (userError) throw userError;

      // Also get telegram user data if it exists
      let telegramData = null;
      if (user) {
        const {data: telegram, error: telegramError} = await supabase
          .from('telegram_users')
          .select('telegram_id, username as telegram_username, first_name, last_name')
          .eq('user_id', user.id)
          .single();

        if (!telegramError && telegram) {
          telegramData = telegram;
        }
      }

      return {
        success: true,
        data: {
          ...user,
          telegram: telegramData
        }
      };
    } catch (error) {
      console.error('Get user profile error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Update user profile
   */
  async updateUserProfile(userId, updates) {
    try {
      const {data, error} = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('auth_user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Update user profile error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get user portfolio (shares and investments) - MATCHES TELEGRAM BOT SCHEMA
   */
  async getUserPortfolio(authUserId) {
    try {
      // Get user's internal ID first using auth_user_id
      const {data: user} = await supabase
        .from('users')
        .select('id, username')
        .eq('auth_user_id', authUserId)
        .single();

      if (!user) throw new Error('User not found');

      // Get share purchases with all relevant fields
      const {data: shares, error: sharesError} = await supabase
        .from('aureus_share_purchases')
        .select(`
          id,
          user_id,
          package_name,
          shares_purchased,
          total_amount,
          commission_used,
          remaining_payment,
          payment_method,
          status,
          created_at,
          updated_at
        `)
        .eq('user_id', user.id)
        .order('created_at', {ascending: false});

      if (sharesError) throw sharesError;

      // Get commission balances with all fields
      const {data: commissions, error: commissionsError} = await supabase
        .from('commission_balances')
        .select(`
          id,
          user_id,
          usdt_balance,
          share_balance,
          total_usdt_earned,
          total_shares_earned,
          last_updated,
          created_at
        `)
        .eq('user_id', user.id);

      if (commissionsError) throw commissionsError;

      // Get current investment phase info
      const {data: currentPhase} = await supabase
        .from('investment_phases')
        .select('id, phase_name, price_per_share, is_active, total_shares_available, shares_sold')
        .eq('is_active', true)
        .single();

      return {
        success: true,
        data: {
          shares: shares || [],
          commissions: commissions || [],
          currentPhase: currentPhase || null,
          user: {
            id: user.id,
            username: user.username
          }
        },
      };
    } catch (error) {
      console.error('Get user portfolio error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Create new share purchase - MATCHES TELEGRAM BOT SCHEMA
   */
  async createSharePurchase(authUserId, purchaseData) {
    try {
      // Get user's internal ID
      const {data: user} = await supabase
        .from('users')
        .select('id, username')
        .eq('auth_user_id', authUserId)
        .single();

      if (!user) throw new Error('User not found');

      // Get current investment phase for share price
      const {data: currentPhase} = await supabase
        .from('investment_phases')
        .select('id, price_per_share, phase_name')
        .eq('is_active', true)
        .single();

      const {data, error} = await supabase
        .from('aureus_share_purchases')
        .insert({
          user_id: user.id,
          package_name: `${currentPhase?.phase_name || 'Pre Sale'} - Mobile App Purchase`,
          shares_purchased: purchaseData.shares_purchased,
          total_amount: purchaseData.total_amount,
          commission_used: 0.00,
          remaining_payment: purchaseData.total_amount,
          payment_method: purchaseData.payment_method || 'usdt',
          status: 'pending', // Always start as pending
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select(`
          id,
          user_id,
          package_name,
          shares_purchased,
          total_amount,
          commission_used,
          remaining_payment,
          payment_method,
          status,
          created_at,
          updated_at
        `)
        .single();

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Create share purchase error:', error);
      return {success: false, error: error.message};
    }
  },
};

/**
 * Real-time subscription helpers
 */
export const realtimeService = {
  /**
   * Subscribe to user portfolio changes
   */
  subscribeToPortfolio(userId, callback) {
    return supabase
      .channel('portfolio-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'aureus_share_purchases',
          filter: `user_id=eq.${userId}`,
        },
        callback,
      )
      .subscribe();
  },

  /**
   * Subscribe to commission balance changes
   */
  subscribeToCommissions(userId, callback) {
    return supabase
      .channel('commission-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'commission_balances',
          filter: `user_id=eq.${userId}`,
        },
        callback,
      )
      .subscribe();
  },

  /**
   * Unsubscribe from channel
   */
  unsubscribe(subscription) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  },
};

export default supabase;
