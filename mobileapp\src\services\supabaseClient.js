/**
 * Aureus Alliance Holdings Mobile App
 * Supabase Client Configuration
 * 
 * This file configures the Supabase client for the mobile app,
 * maintaining compatibility with the existing Telegram bot database.
 */

import {createClient} from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase configuration
// TODO: Replace with your actual Supabase URL and keys
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'your_supabase_anon_key_here';

// Create Supabase client with React Native specific configuration
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    // Use AsyncStorage for session persistence
    storage: AsyncStorage,
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session across app restarts
    persistSession: true,
    // Disable URL detection (not needed for mobile)
    detectSessionInUrl: false,
  },
  // Configure realtime subscriptions
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

/**
 * Authentication service functions
 */
export const authService = {
  /**
   * Sign up new user with email and password
   */
  async signUp(email, password, userData = {}) {
    try {
      const {data, error} = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName,
            phone: userData.phone,
            country: userData.country,
            source: 'mobile_app',
          },
        },
      });

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Sign up error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Sign in existing user
   */
  async signIn(email, password) {
    try {
      const {data, error} = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Sign in error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Sign out current user
   */
  async signOut() {
    try {
      const {error} = await supabase.auth.signOut();
      if (error) throw error;
      return {success: true};
    } catch (error) {
      console.error('Sign out error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Reset password
   */
  async resetPassword(email) {
    try {
      const {error} = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'aureus://reset-password',
      });

      if (error) throw error;
      return {success: true};
    } catch (error) {
      console.error('Reset password error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get current session
   */
  async getSession() {
    try {
      const {data: {session}, error} = await supabase.auth.getSession();
      if (error) throw error;
      return {success: true, session};
    } catch (error) {
      console.error('Get session error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get current user
   */
  async getCurrentUser() {
    try {
      const {data: {user}, error} = await supabase.auth.getUser();
      if (error) throw error;
      return {success: true, user};
    } catch (error) {
      console.error('Get current user error:', error);
      return {success: false, error: error.message};
    }
  },
};

/**
 * Database service functions
 */
export const dbService = {
  /**
   * Get user profile data
   */
  async getUserProfile(userId) {
    try {
      const {data, error} = await supabase
        .from('users')
        .select('*')
        .eq('auth_user_id', userId)
        .single();

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Get user profile error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Update user profile
   */
  async updateUserProfile(userId, updates) {
    try {
      const {data, error} = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('auth_user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Update user profile error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get user portfolio (shares and investments)
   */
  async getUserPortfolio(userId) {
    try {
      // Get user's internal ID first
      const {data: user} = await supabase
        .from('users')
        .select('id')
        .eq('auth_user_id', userId)
        .single();

      if (!user) throw new Error('User not found');

      // Get share purchases
      const {data: shares, error: sharesError} = await supabase
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', {ascending: false});

      if (sharesError) throw sharesError;

      // Get commission balances
      const {data: commissions, error: commissionsError} = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', user.id);

      if (commissionsError) throw commissionsError;

      return {
        success: true,
        data: {
          shares: shares || [],
          commissions: commissions || [],
        },
      };
    } catch (error) {
      console.error('Get user portfolio error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Create new share purchase
   */
  async createSharePurchase(userId, purchaseData) {
    try {
      // Get user's internal ID
      const {data: user} = await supabase
        .from('users')
        .select('id')
        .eq('auth_user_id', userId)
        .single();

      if (!user) throw new Error('User not found');

      const {data, error} = await supabase
        .from('aureus_share_purchases')
        .insert({
          user_id: user.id,
          ...purchaseData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Create share purchase error:', error);
      return {success: false, error: error.message};
    }
  },
};

/**
 * Real-time subscription helpers
 */
export const realtimeService = {
  /**
   * Subscribe to user portfolio changes
   */
  subscribeToPortfolio(userId, callback) {
    return supabase
      .channel('portfolio-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'aureus_share_purchases',
          filter: `user_id=eq.${userId}`,
        },
        callback,
      )
      .subscribe();
  },

  /**
   * Subscribe to commission balance changes
   */
  subscribeToCommissions(userId, callback) {
    return supabase
      .channel('commission-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'commission_balances',
          filter: `user_id=eq.${userId}`,
        },
        callback,
      )
      .subscribe();
  },

  /**
   * Unsubscribe from channel
   */
  unsubscribe(subscription) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  },
};

export default supabase;
