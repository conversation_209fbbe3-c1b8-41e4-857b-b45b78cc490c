const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function checkAuthTokensTable() {
  console.log('🔍 Checking auth_tokens table...');
  
  try {
    // Check if table exists and get some sample data
    const { data, error } = await supabase
      .from('auth_tokens')
      .select('*')
      .limit(3);
    
    if (error) {
      console.log('❌ Table issue:', error.message);
      
      if (error.message.includes('does not exist')) {
        console.log('🔧 Creating auth_tokens table...');
        console.log('📋 SQL to create the table:');
        console.log(`
CREATE TABLE auth_tokens (
  id SERIAL PRIMARY KEY,
  token TEXT UNIQUE NOT NULL,
  telegram_id BIGINT,
  user_data JSONB,
  user_status TEXT,
  confirmed BOOLEA<PERSON> DEFAULT false,
  cancelled BOOLEAN DEFAULT false,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_auth_tokens_token ON auth_tokens(token);
CREATE INDEX idx_auth_tokens_telegram_id ON auth_tokens(telegram_id);
CREATE INDEX idx_auth_tokens_expires_at ON auth_tokens(expires_at);
        `);
      }
    } else {
      console.log('✅ auth_tokens table exists');
      console.log('📊 Sample data:', data);
      
      // Test creating a 6-digit PIN
      const testPin = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 15);
      
      console.log(`🧪 Testing PIN creation: ${testPin}`);
      
      const { data: insertData, error: insertError } = await supabase
        .from('auth_tokens')
        .insert({
          token: testPin,
          confirmed: false,
          cancelled: false,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (insertError) {
        console.error('❌ Error creating test PIN:', insertError);
      } else {
        console.log('✅ Test PIN created successfully:', insertData);
        
        // Clean up test PIN
        await supabase
          .from('auth_tokens')
          .delete()
          .eq('id', insertData.id);
        console.log('🧹 Test PIN cleaned up');
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

checkAuthTokensTable();
