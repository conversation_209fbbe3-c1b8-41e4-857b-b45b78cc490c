// Referral Performance Summary Implementation
// Provides comprehensive performance metrics and summaries
// Date: 2025-01-10

const { db } = require('../src/database/supabase-client');

/**
 * Handle referral performance summary view
 */
async function handleReferralPerformanceSummary(ctx) {
  const user = ctx.from;

  try {
    console.log(`📊 [PERFORMANCE_SUMMARY] Loading performance summary for user ${user.id} (@${user.username})`);

    // Get user ID
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', user.id)
      .single();

    if (telegramError || !telegramUser) {
      await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
      return;
    }

    // Get comprehensive performance data
    const performanceData = await getComprehensivePerformanceData(telegramUser.user_id);

    let summaryMessage = `📊 **REFERRAL PERFORMANCE SUMMARY**\n\n`;

    // Overall Statistics
    summaryMessage += `📈 **OVERALL STATISTICS**\n`;
    summaryMessage += `• **Total Referrals:** ${performanceData.totalReferrals}\n`;
    summaryMessage += `• **Active Referrals:** ${performanceData.activeReferrals}\n`;
    summaryMessage += `• **Lifetime Commission:** $${performanceData.lifetimeCommission.toFixed(2)}\n`;
    summaryMessage += `• **Average per Referral:** $${performanceData.avgCommissionPerReferral.toFixed(2)}\n`;
    summaryMessage += `• **Total Investment Generated:** $${performanceData.totalInvestmentGenerated.toFixed(2)}\n\n`;

    // Recent Performance (Last 30 Days)
    summaryMessage += `🔥 **RECENT PERFORMANCE (30 Days)**\n`;
    summaryMessage += `• **New Referrals:** ${performanceData.recentMetrics.newReferrals}\n`;
    summaryMessage += `• **Commission Earned:** $${performanceData.recentMetrics.recentCommission.toFixed(2)}\n`;
    summaryMessage += `• **Growth Rate:** ${getGrowthRateDisplay(performanceData.recentMetrics.growthRate)}\n`;
    summaryMessage += `• **Active Rate:** ${performanceData.recentMetrics.activeRate.toFixed(1)}%\n\n`;

    // Top Performers
    if (performanceData.topPerformers.length > 0) {
      summaryMessage += `🏆 **TOP PERFORMING REFERRALS**\n`;
      performanceData.topPerformers.slice(0, 5).forEach((performer, index) => {
        const safeUsername = performer.username.replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
        summaryMessage += `${index + 1}. **${safeUsername}**: $${performer.totalCommission.toFixed(2)}\n`;
      });
      summaryMessage += `\n`;
    }

    // Commission Breakdown
    summaryMessage += `💰 **COMMISSION BREAKDOWN**\n`;
    summaryMessage += `• **USDT Commission:** $${performanceData.commissionBreakdown.usdt.toFixed(2)}\n`;
    summaryMessage += `• **Share Commission:** ${performanceData.commissionBreakdown.shares.toFixed(2)} shares\n`;
    summaryMessage += `• **Estimated Share Value:** $${(performanceData.commissionBreakdown.shares * 5.00).toFixed(2)}\n`;
    summaryMessage += `• **Total Estimated Value:** $${(performanceData.commissionBreakdown.usdt + (performanceData.commissionBreakdown.shares * 5.00)).toFixed(2)}\n\n`;

    // Activity Trends
    if (performanceData.activityTrends.length > 0) {
      summaryMessage += `📅 **ACTIVITY TRENDS (Last 7 Days)**\n`;
      performanceData.activityTrends.forEach(trend => {
        summaryMessage += `• **${trend.date}**: $${trend.commission.toFixed(2)} (${trend.transactions} transactions)\n`;
      });
      summaryMessage += `\n`;
    }

    // Performance Insights
    const insights = generatePerformanceInsights(performanceData);
    if (insights.length > 0) {
      summaryMessage += `💡 **PERFORMANCE INSIGHTS**\n`;
      insights.forEach(insight => {
        summaryMessage += `• ${insight}\n`;
      });
      summaryMessage += `\n`;
    }

    // Milestones and Achievements
    const milestones = checkMilestones(performanceData);
    if (milestones.length > 0) {
      summaryMessage += `🎯 **ACHIEVEMENTS & MILESTONES**\n`;
      milestones.forEach(milestone => {
        summaryMessage += `${milestone.icon} ${milestone.description}\n`;
      });
      summaryMessage += `\n`;
    }

    // Next Goals
    const nextGoals = generateNextGoals(performanceData);
    summaryMessage += `🎯 **NEXT GOALS**\n`;
    nextGoals.forEach(goal => {
      summaryMessage += `• ${goal}\n`;
    });

    const keyboard = [
      [
        { text: "📊 Detailed Analytics", callback_data: "view_performance_analytics" },
        { text: "🔍 Individual Details", callback_data: "view_individual_referral_details" }
      ],
      [
        { text: "📈 Commission History", callback_data: "view_detailed_commission_history" },
        { text: "🎯 Optimization Tips", callback_data: "referral_optimization_tips" }
      ],
      [
        { text: "📤 Share Referral Link", callback_data: "share_referral" },
        { text: "💰 View Commission Balance", callback_data: "view_commission" }
      ],
      [
        { text: "🔄 Refresh Summary", callback_data: "referral_performance_summary" },
        { text: "🔙 Back to Referrals", callback_data: "menu_referrals" }
      ]
    ];

    try {
      await ctx.replyWithMarkdown(summaryMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    } catch (markdownError) {
      console.error('❌ Markdown parsing error in performance summary:', markdownError);
      
      // Fallback: Send without markdown formatting
      const plainMessage = summaryMessage.replace(/\*\*/g, '').replace(/`/g, '');
      await ctx.reply(plainMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    }

  } catch (error) {
    console.error('Referral performance summary error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading performance summary**\n\nPlease try again.');
  }
}

/**
 * Get comprehensive performance data for a user
 */
async function getComprehensivePerformanceData(userId) {
  try {
    // Get basic referral counts
    const { data: referrals, error: referralsError } = await db.client
      .from('referrals')
      .select('id, referred_id, created_at, status')
      .eq('referrer_id', userId);

    const totalReferrals = referrals ? referrals.length : 0;
    const activeReferrals = referrals ? referrals.filter(r => r.status === 'active').length : 0;

    // Get all commission transactions
    const { data: commissions, error: commissionsError } = await db.client
      .from('commission_transactions')
      .select(`
        id,
        referred_id,
        usdt_commission,
        share_commission,
        share_purchase_amount,
        payment_date,
        users!commission_transactions_referred_id_fkey (
          username,
          full_name
        )
      `)
      .eq('referrer_id', userId)
      .eq('status', 'approved')
      .order('payment_date', { ascending: false });

    const totalCommissions = commissions || [];
    
    // Calculate lifetime commission
    const lifetimeCommission = totalCommissions.reduce((sum, c) => 
      sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0);
    
    const totalInvestmentGenerated = totalCommissions.reduce((sum, c) => 
      sum + parseFloat(c.share_purchase_amount || 0), 0);
    
    const avgCommissionPerReferral = totalReferrals > 0 ? lifetimeCommission / totalReferrals : 0;

    // Recent metrics (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentCommissions = totalCommissions.filter(c => new Date(c.payment_date) >= thirtyDaysAgo);
    const recentCommissionAmount = recentCommissions.reduce((sum, c) => 
      sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0);
    
    const newReferrals = referrals ? referrals.filter(r => new Date(r.created_at) >= thirtyDaysAgo).length : 0;
    
    // Calculate growth rate (compare with previous 30 days)
    const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000);
    const previousPeriodCommissions = totalCommissions.filter(c => {
      const date = new Date(c.payment_date);
      return date >= sixtyDaysAgo && date < thirtyDaysAgo;
    });
    const previousCommissionAmount = previousPeriodCommissions.reduce((sum, c) => 
      sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0);
    
    let growthRate = 0;
    if (previousCommissionAmount > 0) {
      growthRate = ((recentCommissionAmount - previousCommissionAmount) / previousCommissionAmount) * 100;
    } else if (recentCommissionAmount > 0) {
      growthRate = 100;
    }

    const activeRate = totalReferrals > 0 ? (activeReferrals / totalReferrals) * 100 : 0;

    // Top performers
    const referralPerformance = {};
    totalCommissions.forEach(commission => {
      const referredId = commission.referred_id;
      if (!referralPerformance[referredId]) {
        referralPerformance[referredId] = {
          username: commission.users?.username || commission.users?.full_name || 'Anonymous',
          totalCommission: 0,
          transactionCount: 0
        };
      }
      referralPerformance[referredId].totalCommission += parseFloat(commission.usdt_commission || 0) + parseFloat(commission.share_commission || 0);
      referralPerformance[referredId].transactionCount += 1;
    });

    const topPerformers = Object.values(referralPerformance)
      .sort((a, b) => b.totalCommission - a.totalCommission)
      .slice(0, 10);

    // Commission breakdown
    const usdtCommission = totalCommissions.reduce((sum, c) => sum + parseFloat(c.usdt_commission || 0), 0);
    const shareCommission = totalCommissions.reduce((sum, c) => sum + parseFloat(c.share_commission || 0), 0);

    // Activity trends (last 7 days)
    const activityTrends = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toLocaleDateString();
      const dayCommissions = totalCommissions.filter(c => {
        const commissionDate = new Date(c.payment_date);
        return commissionDate.toDateString() === date.toDateString();
      });
      
      const dayCommissionAmount = dayCommissions.reduce((sum, c) => 
        sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0);
      
      activityTrends.push({
        date: dateStr,
        commission: dayCommissionAmount,
        transactions: dayCommissions.length
      });
    }

    return {
      totalReferrals,
      activeReferrals,
      lifetimeCommission,
      avgCommissionPerReferral,
      totalInvestmentGenerated,
      recentMetrics: {
        newReferrals,
        recentCommission: recentCommissionAmount,
        growthRate,
        activeRate
      },
      topPerformers,
      commissionBreakdown: {
        usdt: usdtCommission,
        shares: shareCommission
      },
      activityTrends
    };

  } catch (error) {
    console.error('Error getting comprehensive performance data:', error);
    return {
      totalReferrals: 0,
      activeReferrals: 0,
      lifetimeCommission: 0,
      avgCommissionPerReferral: 0,
      totalInvestmentGenerated: 0,
      recentMetrics: {
        newReferrals: 0,
        recentCommission: 0,
        growthRate: 0,
        activeRate: 0
      },
      topPerformers: [],
      commissionBreakdown: {
        usdt: 0,
        shares: 0
      },
      activityTrends: []
    };
  }
}

/**
 * Get growth rate display with appropriate emoji
 */
function getGrowthRateDisplay(growthRate) {
  if (growthRate > 0) {
    return `📈 +${growthRate.toFixed(1)}%`;
  } else if (growthRate < 0) {
    return `📉 ${growthRate.toFixed(1)}%`;
  } else {
    return `➡️ 0.0%`;
  }
}

/**
 * Generate performance insights based on data
 */
function generatePerformanceInsights(data) {
  const insights = [];

  // Commission insights
  if (data.lifetimeCommission > 1000) {
    insights.push('🎉 Excellent! You\'ve earned over $1,000 in commissions');
  } else if (data.lifetimeCommission > 500) {
    insights.push('💪 Great progress! You\'re building a solid commission base');
  }

  // Growth insights
  if (data.recentMetrics.growthRate > 50) {
    insights.push('🚀 Outstanding growth rate - your referral strategy is working!');
  } else if (data.recentMetrics.growthRate < -20) {
    insights.push('📉 Commission decline detected - consider re-engaging your network');
  }

  // Activity insights
  if (data.recentMetrics.activeRate > 80) {
    insights.push('⭐ High referral activity rate - excellent network quality');
  } else if (data.recentMetrics.activeRate < 50) {
    insights.push('💡 Consider strategies to increase referral engagement');
  }

  // Referral quality insights
  if (data.avgCommissionPerReferral > 100) {
    insights.push('🎯 High-value referrals - you\'re attracting quality investors');
  }

  return insights;
}

/**
 * Check for milestones and achievements
 */
function checkMilestones(data) {
  const milestones = [];

  // Commission milestones
  if (data.lifetimeCommission >= 10000) {
    milestones.push({ icon: '💎', description: 'Diamond Referrer - $10,000+ lifetime commission' });
  } else if (data.lifetimeCommission >= 5000) {
    milestones.push({ icon: '🥇', description: 'Gold Referrer - $5,000+ lifetime commission' });
  } else if (data.lifetimeCommission >= 1000) {
    milestones.push({ icon: '🥈', description: 'Silver Referrer - $1,000+ lifetime commission' });
  } else if (data.lifetimeCommission >= 100) {
    milestones.push({ icon: '🥉', description: 'Bronze Referrer - $100+ lifetime commission' });
  }

  // Referral count milestones
  if (data.totalReferrals >= 100) {
    milestones.push({ icon: '👑', description: 'Network Builder - 100+ referrals' });
  } else if (data.totalReferrals >= 50) {
    milestones.push({ icon: '🌟', description: 'Community Leader - 50+ referrals' });
  } else if (data.totalReferrals >= 10) {
    milestones.push({ icon: '🎯', description: 'Active Referrer - 10+ referrals' });
  }

  // Growth milestones
  if (data.recentMetrics.growthRate > 100) {
    milestones.push({ icon: '🚀', description: 'Rapid Growth - 100%+ monthly growth' });
  }

  return milestones;
}

/**
 * Generate next goals based on current performance
 */
function generateNextGoals(data) {
  const goals = [];

  // Commission goals
  if (data.lifetimeCommission < 100) {
    goals.push('Reach your first $100 in lifetime commissions');
  } else if (data.lifetimeCommission < 1000) {
    goals.push('Achieve $1,000 in lifetime commissions');
  } else if (data.lifetimeCommission < 5000) {
    goals.push('Reach $5,000 in lifetime commissions for Gold status');
  } else {
    goals.push('Maintain your excellent commission performance');
  }

  // Referral goals
  if (data.totalReferrals < 10) {
    goals.push('Build your network to 10 active referrals');
  } else if (data.totalReferrals < 50) {
    goals.push('Expand to 50 referrals for Community Leader status');
  } else {
    goals.push('Continue growing your referral network');
  }

  // Activity goals
  if (data.recentMetrics.activeRate < 70) {
    goals.push('Increase referral engagement rate to 70%+');
  }

  if (data.recentMetrics.newReferrals === 0) {
    goals.push('Add new referrals this month to maintain growth');
  }

  return goals;
}

module.exports = {
  handleReferralPerformanceSummary,
  getComprehensivePerformanceData,
  getGrowthRateDisplay,
  generatePerformanceInsights,
  checkMilestones,
  generateNextGoals
};
