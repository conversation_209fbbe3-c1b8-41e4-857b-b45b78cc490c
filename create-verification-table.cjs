const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function createEmailVerificationTable() {
  console.log('🔧 Creating email_verification_codes table...');
  
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS email_verification_codes (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      email TEXT NOT NULL,
      code_hash TEXT NOT NULL,
      purpose TEXT NOT NULL CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection')),
      expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
      attempts INTEGER DEFAULT 0,
      verified_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON email_verification_codes(email);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_purpose ON email_verification_codes(purpose);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
  `;
  
  try {
    // Execute the SQL directly using a raw query
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: createTableSQL 
    });
    
    if (error) {
      console.error('❌ Failed to create table via RPC:', error);
      
      // Try alternative approach - create table parts separately
      console.log('🔄 Trying alternative approach...');
      
      const queries = [
        `CREATE TABLE IF NOT EXISTS email_verification_codes (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
          email TEXT NOT NULL,
          code_hash TEXT NOT NULL,
          purpose TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          attempts INTEGER DEFAULT 0,
          verified_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )`,
        `ALTER TABLE email_verification_codes 
         ADD CONSTRAINT IF NOT EXISTS email_verification_codes_purpose_check 
         CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection'))`,
        `CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id)`,
        `CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON email_verification_codes(email)`,
        `CREATE INDEX IF NOT EXISTS idx_email_verification_codes_purpose ON email_verification_codes(purpose)`,
        `CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires_at ON email_verification_codes(expires_at)`
      ];
      
      for (let i = 0; i < queries.length; i++) {
        const query = queries[i];
        console.log(`📝 Executing query ${i + 1}/${queries.length}...`);
        
        const { error: queryError } = await supabase.rpc('exec_sql', { 
          sql_query: query 
        });
        
        if (queryError) {
          console.error(`❌ Query ${i + 1} failed:`, queryError);
        } else {
          console.log(`✅ Query ${i + 1} successful`);
        }
      }
    } else {
      console.log('✅ Table created successfully');
    }
    
    // Test the table
    console.log('🧪 Testing table...');
    const { data: testData, error: testError } = await supabase
      .from('email_verification_codes')
      .insert({
        user_id: 106,
        email: '<EMAIL>',
        code_hash: 'test_hash_123',
        purpose: 'telegram_connection',
        expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        attempts: 0
      })
      .select()
      .single();
    
    if (testError) {
      console.error('❌ Table test failed:', testError);
    } else {
      console.log('✅ Table test successful!');
      console.log('📊 Test record:', testData);
      
      // Clean up test record
      await supabase
        .from('email_verification_codes')
        .delete()
        .eq('id', testData.id);
      console.log('🧹 Test record cleaned up');
    }
    
  } catch (error) {
    console.error('❌ Exception:', error);
  }
}

createEmailVerificationTable();
