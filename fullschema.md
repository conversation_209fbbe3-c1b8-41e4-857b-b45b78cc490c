[{"table_schema": "auth", "table_name": "audit_log_entries"}, {"table_schema": "auth", "table_name": "flow_state"}, {"table_schema": "auth", "table_name": "identities"}, {"table_schema": "auth", "table_name": "instances"}, {"table_schema": "auth", "table_name": "mfa_amr_claims"}, {"table_schema": "auth", "table_name": "mfa_challenges"}, {"table_schema": "auth", "table_name": "mfa_factors"}, {"table_schema": "auth", "table_name": "one_time_tokens"}, {"table_schema": "auth", "table_name": "refresh_tokens"}, {"table_schema": "auth", "table_name": "saml_providers"}, {"table_schema": "auth", "table_name": "saml_relay_states"}, {"table_schema": "auth", "table_name": "schema_migrations"}, {"table_schema": "auth", "table_name": "sessions"}, {"table_schema": "auth", "table_name": "sso_domains"}, {"table_schema": "auth", "table_name": "sso_providers"}, {"table_schema": "auth", "table_name": "users"}, {"table_schema": "public", "table_name": "account_links"}, {"table_schema": "public", "table_name": "account_merge_log"}, {"table_schema": "public", "table_name": "admin_audit_logs"}, {"table_schema": "public", "table_name": "admin_notification_preferences"}, {"table_schema": "public", "table_name": "admin_users"}, {"table_schema": "public", "table_name": "aureus_share_purchases"}, {"table_schema": "public", "table_name": "auth_tokens"}, {"table_schema": "public", "table_name": "certificates"}, {"table_schema": "public", "table_name": "commission_balances"}, {"table_schema": "public", "table_name": "commission_conversions"}, {"table_schema": "public", "table_name": "commission_escrow"}, {"table_schema": "public", "table_name": "commission_transactions"}, {"table_schema": "public", "table_name": "commission_usage"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests"}, {"table_schema": "public", "table_name": "commission_withdrawals"}, {"table_schema": "public", "table_name": "company_wallets"}, {"table_schema": "public", "table_name": "country_change_log"}, {"table_schema": "public", "table_name": "crypto_payment_transactions"}, {"table_schema": "public", "table_name": "document_access_logs"}, {"table_schema": "public", "table_name": "financial_audit_log"}, {"table_schema": "public", "table_name": "gallery_categories"}, {"table_schema": "public", "table_name": "gallery_images"}, {"table_schema": "public", "table_name": "investment_phases"}, {"table_schema": "public", "table_name": "kyc_audit_log"}, {"table_schema": "public", "table_name": "kyc_information"}, {"table_schema": "public", "table_name": "marketing_content_generated"}, {"table_schema": "public", "table_name": "marketing_materials"}, {"table_schema": "public", "table_name": "marketing_training_progress"}, {"table_schema": "public", "table_name": "nda_acceptances"}, {"table_schema": "public", "table_name": "notification_log"}, {"table_schema": "public", "table_name": "notification_sound_types"}, {"table_schema": "public", "table_name": "payment_admin_notes"}, {"table_schema": "public", "table_name": "payment_allocations"}, {"table_schema": "public", "table_name": "referrals"}, {"table_schema": "public", "table_name": "section_balances"}, {"table_schema": "public", "table_name": "site_content"}, {"table_schema": "public", "table_name": "supported_countries"}, {"table_schema": "public", "table_name": "sync_notifications"}, {"table_schema": "public", "table_name": "system_settings"}, {"table_schema": "public", "table_name": "telegram_sync_requests"}, {"table_schema": "public", "table_name": "telegram_users"}, {"table_schema": "public", "table_name": "terms_acceptance"}, {"table_schema": "public", "table_name": "test_connection"}, {"table_schema": "public", "table_name": "test_user_exclusion_audit"}, {"table_schema": "public", "table_name": "test_user_exclusions"}, {"table_schema": "public", "table_name": "user_messages"}, {"table_schema": "public", "table_name": "user_notification_preferences"}, {"table_schema": "public", "table_name": "user_notification_summary"}, {"table_schema": "public", "table_name": "user_notifications"}, {"table_schema": "public", "table_name": "user_payment_methods"}, {"table_schema": "public", "table_name": "user_preferences"}, {"table_schema": "public", "table_name": "user_sessions"}, {"table_schema": "public", "table_name": "users"}, {"table_schema": "public", "table_name": "withdrawal_invoices"}, {"table_schema": "realtime", "table_name": "messages"}, {"table_schema": "realtime", "table_name": "schema_migrations"}, {"table_schema": "realtime", "table_name": "subscription"}, {"table_schema": "storage", "table_name": "buckets"}, {"table_schema": "storage", "table_name": "migrations"}, {"table_schema": "storage", "table_name": "objects"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts"}, {"table_schema": "vault", "table_name": "secrets"}]