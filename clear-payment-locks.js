const { createClient } = require('@supabase/supabase-js');

console.log('🚀 Starting payment lock clearing script...');

// Try to load .env if it exists, but don't fail if it doesn't
try {
  require('dotenv').config();
} catch (e) {
  console.log('📝 No .env file found, using environment variables directly');
}

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkAndClearLock() {
  try {
    console.log('🔍 Checking for locked users...');
    
    const { data: lockedUsers, error } = await supabase
      .from('telegram_users')
      .select('telegram_id, user_id, payment_lock_status, payment_context, payment_lock_timestamp')
      .eq('payment_lock_status', true);
    
    if (error) {
      console.error('❌ Error checking locks:', error);
      return;
    }
    
    console.log(`📊 Currently locked users: ${lockedUsers?.length || 0}`);
    
    if (lockedUsers && lockedUsers.length > 0) {
      console.log('📋 Locked users details:');
      lockedUsers.forEach((user, index) => {
        console.log(`${index + 1}. Telegram ID: ${user.telegram_id}, User ID: ${user.user_id}`);
        console.log(`   Lock timestamp: ${user.payment_lock_timestamp}`);
        console.log(`   Context: ${JSON.stringify(user.payment_context, null, 2)}`);
        console.log('');
      });
      
      console.log('🧹 Clearing all payment locks...');
      const { error: clearError } = await supabase
        .from('telegram_users')
        .update({ 
          payment_lock_status: false, 
          payment_context: null, 
          payment_lock_timestamp: null 
        })
        .eq('payment_lock_status', true);
      
      if (clearError) {
        console.error('❌ Error clearing locks:', clearError);
      } else {
        console.log('✅ All payment locks cleared successfully');
        console.log(`🎉 Cleared locks for ${lockedUsers.length} users`);
      }
    } else {
      console.log('✅ No users are currently locked');
    }
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkAndClearLock().then(() => {
  console.log('🏁 Script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
