// Enhanced Referral Dashboard Implementation
// Provides detailed commission tracking and transparency for users
// Date: 2025-01-10

const { db } = require('../src/database/supabase-client');

/**
 * Safely format username for Telegram Markdown
 */
function sanitizeUsername(username) {
  if (!username) return 'Anonymous';
  // Only escape the most problematic characters for Telegram Markdown
  return username.replace(/[_*`[\]]/g, '\\$&');
}

/**
 * Enhanced referral dashboard with detailed commission breakdown
 */
async function handleEnhancedReferralDashboard(ctx) {
  const user = ctx.from;

  try {
    console.log(`🔍 [ENHANCED_REFERRAL] Loading enhanced dashboard for user ${user.id} (@${user.username})`);

    // Get user ID from telegram_users table
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', user.id)
      .single();

    if (telegramError || !telegramUser) {
      await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
      return;
    }

    // Get detailed referral commission breakdown using direct queries
    const { data: commissionBreakdown, error: breakdownError } = await db.client
      .from('commission_transactions')
      .select(`
        referred_id,
        usdt_commission,
        share_commission,
        share_purchase_amount,
        payment_date,
        users!commission_transactions_referred_id_fkey (
          username,
          full_name,
          created_at
        )
      `)
      .eq('referrer_id', telegramUser.user_id)
      .eq('status', 'approved')
      .order('payment_date', { ascending: false });

    if (breakdownError) {
      console.error('Error fetching commission breakdown:', breakdownError);
      await ctx.replyWithMarkdown('❌ **Error loading referral data**\n\nPlease try again.');
      return;
    }

    // Process the commission data to create breakdown by referral
    const referralBreakdown = {};
    if (commissionBreakdown && commissionBreakdown.length > 0) {
      commissionBreakdown.forEach(commission => {
        const referredId = commission.referred_id;
        if (!referralBreakdown[referredId]) {
          referralBreakdown[referredId] = {
            username: commission.users?.username || commission.users?.full_name || 'Anonymous',
            join_date: commission.users?.created_at,
            total_commission: 0,
            commission_count: 0,
            total_investment_generated: 0,
            last_commission_date: null
          };
        }

        const totalCommission = parseFloat(commission.usdt_commission || 0) + parseFloat(commission.share_commission || 0);
        referralBreakdown[referredId].total_commission += totalCommission;
        referralBreakdown[referredId].commission_count += 1;
        referralBreakdown[referredId].total_investment_generated += parseFloat(commission.share_purchase_amount || 0);

        if (!referralBreakdown[referredId].last_commission_date ||
            new Date(commission.payment_date) > new Date(referralBreakdown[referredId].last_commission_date)) {
          referralBreakdown[referredId].last_commission_date = commission.payment_date;
        }
      });
    }

    // Convert to array format expected by the rest of the function
    const processedBreakdown = Object.values(referralBreakdown);

    // Calculate performance summary from the commission data
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentCommissions = commissionBreakdown ? commissionBreakdown.filter(c =>
      new Date(c.payment_date) >= thirtyDaysAgo
    ) : [];

    const performanceSummary = {
      total_referrals: Object.keys(referralBreakdown).length,
      total_commission: Object.values(referralBreakdown).reduce((sum, ref) => sum + ref.total_commission, 0),
      recent_commission: recentCommissions.reduce((sum, c) =>
        sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0),
      total_investment: Object.values(referralBreakdown).reduce((sum, ref) => sum + ref.total_investment_generated, 0)
    };

    // Build the enhanced dashboard message
    let dashboardMessage = `👥 **ENHANCED REFERRAL DASHBOARD**\n\n`;

    // Performance Summary Section
    dashboardMessage += `📊 **PERFORMANCE SUMMARY**\n`;
    dashboardMessage += `• **Total Referrals:** ${performanceSummary.total_referrals}\n`;
    dashboardMessage += `• **Total Commission:** $${performanceSummary.total_commission.toFixed(2)}\n`;
    dashboardMessage += `• **Recent Commission (30d):** $${performanceSummary.recent_commission.toFixed(2)}\n`;
    dashboardMessage += `• **Total Investment Generated:** $${performanceSummary.total_investment.toFixed(2)}\n`;

    if (performanceSummary.total_referrals > 0) {
      const avgPerReferral = performanceSummary.total_commission / performanceSummary.total_referrals;
      dashboardMessage += `• **Avg per Referral:** $${avgPerReferral.toFixed(2)}\n`;
    }

    dashboardMessage += `\n`;

    // Individual Referral Breakdown
    if (processedBreakdown && processedBreakdown.length > 0) {
      dashboardMessage += `💰 **INDIVIDUAL REFERRAL BREAKDOWN**\n\n`;

      // Sort by total commission (highest first)
      const sortedReferrals = processedBreakdown.sort((a, b) => b.total_commission - a.total_commission);

      sortedReferrals.slice(0, 10).forEach((referral, index) => {
        const joinDate = referral.join_date ?
          new Date(referral.join_date).toLocaleDateString() : 'Unknown';
        const lastCommissionDate = referral.last_commission_date ?
          new Date(referral.last_commission_date).toLocaleDateString() : 'Never';

        // Safely format username to avoid Markdown parsing issues
        const safeUsername = sanitizeUsername(referral.username);

        dashboardMessage += `**${index + 1}. ${safeUsername}**\n`;
        dashboardMessage += `   💰 Total Commission: $${referral.total_commission.toFixed(2)}\n`;
        dashboardMessage += `   📅 Joined: ${joinDate}\n`;
        dashboardMessage += `   🔄 Transactions: ${referral.commission_count}\n`;
        dashboardMessage += `   📈 Last Commission: ${lastCommissionDate}\n`;
        dashboardMessage += `   💵 Generated Investment: $${referral.total_investment_generated.toFixed(2)}\n\n`;
      });

      if (processedBreakdown.length > 10) {
        dashboardMessage += `*... and ${processedBreakdown.length - 10} more referrals*\n\n`;
      }
    } else {
      dashboardMessage += `💡 **NO REFERRALS YET**\n\n`;
      dashboardMessage += `Start sharing your referral link to build your commission network!\n\n`;
    }

    // Top Performer Highlight
    if (processedBreakdown && processedBreakdown.length > 0) {
      const topPerformer = processedBreakdown.sort((a, b) => b.total_commission - a.total_commission)[0];
      const safeTopPerformerName = sanitizeUsername(topPerformer.username);
      dashboardMessage += `🏆 **TOP PERFORMER**\n`;
      dashboardMessage += `• **${safeTopPerformerName}**: $${topPerformer.total_commission.toFixed(2)} (${topPerformer.commission_count} transactions)\n\n`;
    }

    // Recent Activity
    if (commissionBreakdown && commissionBreakdown.length > 0) {
      dashboardMessage += `🔔 **RECENT ACTIVITY**\n`;
      const recentActivity = commissionBreakdown.slice(0, 5);
      recentActivity.forEach(activity => {
        const activityDate = new Date(activity.payment_date).toLocaleDateString();
        const username = sanitizeUsername(activity.users?.username || activity.users?.full_name);
        const commission = parseFloat(activity.usdt_commission || 0) + parseFloat(activity.share_commission || 0);
        dashboardMessage += `💰 Commission from ${username}: $${commission.toFixed(2)} (${activityDate})\n`;
      });
      dashboardMessage += `\n`;
    }

    // Commission Breakdown by Type
    if (commissionBreakdown && commissionBreakdown.length > 0) {
      const totalUSDT = commissionBreakdown.reduce((sum, transaction) => {
        return sum + parseFloat(transaction.usdt_commission || 0);
      }, 0);
      const totalShares = commissionBreakdown.reduce((sum, transaction) => {
        return sum + parseFloat(transaction.share_commission || 0);
      }, 0);

      dashboardMessage += `📈 **COMMISSION BREAKDOWN**\n`;
      dashboardMessage += `• **USDT Commission:** $${totalUSDT.toFixed(2)}\n`;
      dashboardMessage += `• **Share Commission:** ${totalShares.toFixed(2)} shares\n`;
      dashboardMessage += `• **Total Value:** $${(totalUSDT + totalShares * 5.00).toFixed(2)} (est.)\n\n`;
    }

    // Action buttons
    const keyboard = [
      [
        { text: "📊 Detailed Commission History", callback_data: "view_detailed_commission_history" },
        { text: "🔍 Individual Referral Details", callback_data: "view_individual_referral_details" }
      ],
      [
        { text: "📈 Performance Analytics", callback_data: "view_performance_analytics" },
        { text: "🎯 Commission Calculator", callback_data: "commission_calculator" }
      ],
      [
        { text: "📤 Share Referral Link", callback_data: "share_referral" },
        { text: "💰 View Commission Balance", callback_data: "view_commission" }
      ],
      [
        { text: "🔄 Refresh Dashboard", callback_data: "enhanced_referral_dashboard" },
        { text: "🔙 Back to Main Referrals", callback_data: "menu_referrals" }
      ]
    ];

    try {
      await ctx.replyWithMarkdown(dashboardMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    } catch (markdownError) {
      console.error('❌ Markdown parsing error in enhanced referral dashboard:', markdownError);
      
      // Fallback: Send without markdown formatting
      const plainMessage = dashboardMessage.replace(/\*\*/g, '').replace(/`/g, '');
      await ctx.reply(plainMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    }

  } catch (error) {
    console.error('Enhanced referral dashboard error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading enhanced referral dashboard**\n\nPlease try again.');
  }
}

/**
 * Get activity icon based on activity type
 */
function getActivityIcon(activityType) {
  const icons = {
    'registration': '👤',
    'first_purchase': '🛒',
    'commission_earned': '💰',
    'milestone_reached': '🏆',
    'withdrawal': '💸',
    'conversion': '🔄'
  };
  return icons[activityType] || '📝';
}

/**
 * Handle detailed commission history view
 */
async function handleDetailedCommissionHistory(ctx) {
  const user = ctx.from;

  try {
    // Get user ID
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', user.id)
      .single();

    if (telegramError || !telegramUser) {
      await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
      return;
    }

    // Get detailed commission transactions
    const { data: commissionHistory, error: historyError } = await db.client
      .from('commission_transactions')
      .select(`
        id,
        referred_id,
        usdt_commission,
        share_commission,
        share_purchase_amount,
        payment_date,
        commission_rate,
        shares_purchased,
        notes,
        users!commission_transactions_referred_id_fkey (
          username,
          full_name
        )
      `)
      .eq('referrer_id', telegramUser.user_id)
      .eq('status', 'approved')
      .order('payment_date', { ascending: false })
      .limit(50);

    if (historyError) {
      console.error('Error fetching commission history:', historyError);
      await ctx.replyWithMarkdown('❌ **Error loading commission history**\n\nPlease try again.');
      return;
    }

    let historyMessage = `📊 **DETAILED COMMISSION HISTORY**\n\n`;

    if (commissionHistory && commissionHistory.length > 0) {
      historyMessage += `**Recent Commission Transactions (Last 50):**\n\n`;
      
      commissionHistory.forEach((transaction, index) => {
        const date = new Date(transaction.payment_date).toLocaleDateString();
        const username = (transaction.users?.username || transaction.users?.full_name || 'Anonymous')
          .replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
        const usdtCommission = parseFloat(transaction.usdt_commission || 0);
        const shareCommission = parseFloat(transaction.share_commission || 0);
        const purchaseAmount = parseFloat(transaction.share_purchase_amount || 0);
        const commissionRate = parseFloat(transaction.commission_rate || 0);
        const sharesPurchased = parseFloat(transaction.shares_purchased || 0);

        historyMessage += `**${index + 1}. ${username}** (${date})\n`;
        historyMessage += `   💰 USDT: $${usdtCommission.toFixed(2)} | 📈 Shares: ${shareCommission.toFixed(2)}\n`;
        historyMessage += `   🛒 Purchase: $${purchaseAmount.toFixed(2)} | 📊 Rate: ${commissionRate.toFixed(1)}%\n`;
        historyMessage += `   📦 Shares Purchased: ${sharesPurchased.toFixed(2)}\n`;
        
        // Add commission details if available
        if (transaction.commission_details && Object.keys(transaction.commission_details).length > 0) {
          const details = transaction.commission_details;
          if (details.commission_rate) {
            historyMessage += `   📊 Rate: ${details.commission_rate}%\n`;
          }
        }
        
        historyMessage += `\n`;
      });
      
      // Summary statistics
      const totalUSDT = commissionHistory.reduce((sum, t) => sum + parseFloat(t.usdt_commission || 0), 0);
      const totalShares = commissionHistory.reduce((sum, t) => sum + parseFloat(t.share_commission || 0), 0);
      const totalInvestment = commissionHistory.reduce((sum, t) => sum + parseFloat(t.share_purchase_amount || 0), 0);
      
      historyMessage += `📈 **SUMMARY STATISTICS**\n`;
      historyMessage += `• **Total USDT Earned:** $${totalUSDT.toFixed(2)}\n`;
      historyMessage += `• **Total Shares Earned:** ${totalShares.toFixed(2)}\n`;
      historyMessage += `• **Total Investment Generated:** $${totalInvestment.toFixed(2)}\n`;
      historyMessage += `• **Average Commission per Transaction:** $${((totalUSDT + totalShares) / commissionHistory.length).toFixed(2)}\n`;
      
    } else {
      historyMessage += `💡 **NO COMMISSION HISTORY YET**\n\n`;
      historyMessage += `Start referring users to build your commission history!\n`;
    }

    const keyboard = [
      [
        { text: "📊 Export History (CSV)", callback_data: "export_commission_history" },
        { text: "📈 View Analytics", callback_data: "view_performance_analytics" }
      ],
      [
        { text: "🔄 Refresh History", callback_data: "view_detailed_commission_history" },
        { text: "🔙 Back to Dashboard", callback_data: "enhanced_referral_dashboard" }
      ]
    ];

    try {
      await ctx.replyWithMarkdown(historyMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    } catch (markdownError) {
      console.error('❌ Markdown parsing error in commission history:', markdownError);
      
      // Fallback: Send without markdown formatting
      const plainMessage = historyMessage.replace(/\*\*/g, '').replace(/`/g, '');
      await ctx.reply(plainMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    }

  } catch (error) {
    console.error('Detailed commission history error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading commission history**\n\nPlease try again.');
  }
}

module.exports = {
  handleEnhancedReferralDashboard,
  handleDetailedCommissionHistory,
  getActivityIcon
};
