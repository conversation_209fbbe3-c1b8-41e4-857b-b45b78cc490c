#!/usr/bin/env node

/**
 * CRITICAL FIX: Commission Conversion Status Issue
 * 
 * This script fixes the issue where commission-to-shares conversions
 * create share purchase records with 'pending' status instead of 'active' status.
 * 
 * The problem: Users see "Pending Shares: X shares" even after their commission
 * conversion has been approved and processed.
 * 
 * The fix: 
 * 1. Update existing commission conversion purchases to 'active' status
 * 2. Fix the process_commission_conversion SQL function to create 'active' records
 */

const { db } = require('./src/database/supabase-client');
const fs = require('fs');
const path = require('path');

// Use existing database client
const supabase = db.client;

async function deployFix() {
  try {
    console.log('🔧 COMMISSION CONVERSION STATUS FIX');
    console.log('=====================================');
    console.log('');
    
    // Check current state before fix
    console.log('📊 Checking current commission conversion purchases...');
    
    const { data: beforeFix, error: beforeError } = await supabase
      .from('aureus_share_purchases')
      .select('id, user_id, shares_purchased, payment_method, status, created_at')
      .eq('payment_method', 'Commission Conversion')
      .order('created_at', { ascending: false });
    
    if (beforeError) {
      console.error('❌ Error checking current state:', beforeError);
    } else {
      console.log(`📈 Found ${beforeFix?.length || 0} commission conversion purchases`);
      
      const pendingCount = beforeFix?.filter(p => p.status === 'pending').length || 0;
      const activeCount = beforeFix?.filter(p => p.status === 'active').length || 0;
      
      console.log(`   - Pending: ${pendingCount} (these will be fixed)`);
      console.log(`   - Active: ${activeCount} (already correct)`);
      console.log('');
    }
    
    // Read and execute the SQL fix
    console.log('📖 Reading SQL fix file...');
    
    const sqlFilePath = path.join(__dirname, 'fix-commission-conversion-status.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📝 SQL fix file loaded successfully');
    console.log(`📊 File size: ${sqlContent.length} characters`);
    console.log('');
    
    console.log('🔧 Executing SQL fix...');
    
    // Execute the SQL fix
    const { data, error } = await supabase.rpc('exec', {
      sql: sqlContent
    });
    
    if (error) {
      console.error('❌ SQL execution failed:', error);
      throw error;
    }
    
    console.log('✅ SQL fix executed successfully');
    console.log('');
    
    // Check state after fix
    console.log('🔍 Verifying fix results...');
    
    const { data: afterFix, error: afterError } = await supabase
      .from('aureus_share_purchases')
      .select('id, user_id, shares_purchased, payment_method, status, created_at')
      .eq('payment_method', 'Commission Conversion')
      .order('created_at', { ascending: false });
    
    if (afterError) {
      console.error('❌ Error checking results:', afterError);
    } else {
      console.log(`📈 Commission conversion purchases after fix: ${afterFix?.length || 0}`);
      
      const pendingCountAfter = afterFix?.filter(p => p.status === 'pending').length || 0;
      const activeCountAfter = afterFix?.filter(p => p.status === 'active').length || 0;
      
      console.log(`   - Pending: ${pendingCountAfter} (should be 0)`);
      console.log(`   - Active: ${activeCountAfter} (should be all)`);
      
      if (pendingCountAfter === 0) {
        console.log('✅ All commission conversion purchases are now active!');
      } else {
        console.log('⚠️  Some purchases are still pending - manual review needed');
      }
    }
    
    console.log('');
    console.log('🎉 Commission Conversion Status Fix Complete!');
    console.log('');
    console.log('📋 Fix Summary:');
    console.log('✅ Updated existing commission conversion purchases to active status');
    console.log('✅ Fixed process_commission_conversion SQL function');
    console.log('✅ Future commission conversions will be created as active');
    console.log('✅ Users will no longer see "Pending Shares" for processed conversions');
    console.log('');
    console.log('🔍 Next Steps:');
    console.log('1. Monitor user reports to confirm issue is resolved');
    console.log('2. Test new commission conversions to verify they show as active');
    console.log('3. Check that portfolio displays are now correct');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  }
}

// Run the deployment
deployFix();
