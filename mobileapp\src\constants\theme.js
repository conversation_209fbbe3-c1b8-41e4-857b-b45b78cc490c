/**
 * Aureus Alliance Holdings Mobile App
 * Theme Configuration - Colors, Typography, and Styling
 */

import {DefaultTheme} from 'react-native-paper';

export const colors = {
  // Primary brand colors
  primary: '#1E3A8A',      // Dark blue
  primaryLight: '#3B82F6', // Light blue
  secondary: '#FFD700',    // Gold
  secondaryLight: '#FEF3C7', // Light gold
  
  // Status colors
  success: '#10B981',      // Green
  warning: '#F59E0B',      // Orange
  error: '#EF4444',        // Red
  info: '#3B82F6',         // Blue
  
  // Neutral colors
  background: '#F8FAFC',   // Light gray background
  surface: '#FFFFFF',      // White surface
  text: '#1F2937',         // Dark gray text
  textSecondary: '#6B7280', // Medium gray text
  textLight: '#9CA3AF',    // Light gray text
  
  // Border and divider colors
  border: '#E5E7EB',       // Light border
  divider: '#F3F4F6',      // Very light divider
  
  // Investment specific colors
  profit: '#10B981',       // Green for profits
  loss: '#EF4444',         // Red for losses
  pending: '#F59E0B',      // Orange for pending
  
  // Crypto colors
  usdt: '#26A17B',         // USDT green
  bitcoin: '#F7931A',      // Bitcoin orange
  ethereum: '#627EEA',     // Ethereum blue
};

export const typography = {
  // Font families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  
  // Font weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  // Line heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
};

// React Native Paper theme configuration
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.secondary,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    error: colors.error,
    success: colors.success,
    warning: colors.warning,
    info: colors.info,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: typography.fontWeight.medium,
    },
    bold: {
      fontFamily: typography.fontFamily.bold,
      fontWeight: typography.fontWeight.bold,
    },
  },
};

// Common styles that can be reused across components
export const commonStyles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.md,
  },
  
  button: {
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    fontSize: typography.fontSize.base,
    backgroundColor: colors.surface,
  },
  
  text: {
    color: colors.text,
    fontSize: typography.fontSize.base,
    lineHeight: typography.fontSize.base * typography.lineHeight.normal,
  },
  
  heading: {
    color: colors.text,
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    lineHeight: typography.fontSize.xl * typography.lineHeight.tight,
  },
  
  subheading: {
    color: colors.textSecondary,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.fontSize.lg * typography.lineHeight.normal,
  },
  
  caption: {
    color: colors.textLight,
    fontSize: typography.fontSize.sm,
    lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
  },
};
