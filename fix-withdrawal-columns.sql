-- CRITICAL FIX: Add missing columns to commission_withdrawals table
-- This fixes the withdrawal system that's currently failing
-- Run this SQL directly in Supabase Dashboard > SQL Editor

BEGIN;

-- First, check current commission_withdrawals table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'commission_withdrawals' 
ORDER BY ordinal_position;

-- Add missing fee tracking columns
ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS transaction_fee DECIMAL(10,2) DEFAULT 2.00;

ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS net_amount DECIMAL(10,2);

-- Add missing network and tracking columns
ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS network VARCHAR(10);

ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS transaction_hash VARCHAR(255);

ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS proof_of_payment_url VARCHAR(500);

ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS user_confirmed_receipt BOOLEAN DEFAULT FALSE;

ALTER TABLE commission_withdrawals 
ADD COLUMN IF NOT EXISTS confirmation_timestamp TIMESTAMPTZ;

-- Update existing withdrawals to have fee data (assuming $2 fee for all)
UPDATE commission_withdrawals 
SET 
    transaction_fee = 2.00,
    net_amount = amount - 2.00
WHERE transaction_fee IS NULL OR net_amount IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_transaction_fee 
ON commission_withdrawals(transaction_fee);

CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_net_amount 
ON commission_withdrawals(net_amount);

CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_network 
ON commission_withdrawals(network);

CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_transaction_hash 
ON commission_withdrawals(transaction_hash);

CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_user_confirmed 
ON commission_withdrawals(user_confirmed_receipt);

-- Add comments for documentation
COMMENT ON COLUMN commission_withdrawals.transaction_fee IS 'Transaction fee charged for withdrawal (typically $2 USDT)';
COMMENT ON COLUMN commission_withdrawals.net_amount IS 'Net amount user receives after deducting transaction fee';
COMMENT ON COLUMN commission_withdrawals.network IS 'Blockchain network used for withdrawal (ETH, BSC, POL, TRON)';
COMMENT ON COLUMN commission_withdrawals.transaction_hash IS 'Blockchain transaction hash for the payment';
COMMENT ON COLUMN commission_withdrawals.proof_of_payment_url IS 'URL to uploaded proof of payment file';
COMMENT ON COLUMN commission_withdrawals.user_confirmed_receipt IS 'Whether user confirmed receipt of payment';
COMMENT ON COLUMN commission_withdrawals.confirmation_timestamp IS 'When user confirmed receipt';

-- Verify the table structure after changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'commission_withdrawals' 
ORDER BY ordinal_position;

-- Test that we can insert a withdrawal with all required fields
SELECT 'commission_withdrawals table structure updated successfully!' as status;

COMMIT;
