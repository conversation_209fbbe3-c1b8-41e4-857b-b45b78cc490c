/**
 * Aureus Alliance Holdings Mobile App
 * Main App Component
 */

import React, {useEffect, useState} from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {Provider as PaperProvider} from 'react-native-paper';
import {SafeAreaProvider} from 'react-native-safe-area-context';

// Import navigation and context providers
import AppNavigator from './navigation/AppNavigator';
import {AuthProvider} from './context/AuthContext';
import {PortfolioProvider} from './context/PortfolioContext';
import {theme} from './constants/theme';
import {supabase} from './services/supabaseClient';

// Import polyfills for React Native
import 'react-native-url-polyfill/auto';

const App = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [initialRoute, setInitialRoute] = useState('Splash');

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const {data: {session}} = await supabase.auth.getSession();
      
      if (session) {
        setInitialRoute('MainTabs');
      } else {
        setInitialRoute('Auth');
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      setInitialRoute('Auth');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading Aureus App...</Text>
      </View>
    );
  }

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <PortfolioProvider>
            <NavigationContainer>
              <StatusBar
                barStyle="light-content"
                backgroundColor={theme.colors.primary}
              />
              <AppNavigator initialRoute={initialRoute} />
            </NavigationContainer>
          </PortfolioProvider>
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1E3A8A',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#FFD700',
    fontWeight: '600',
  },
});

export default App;
