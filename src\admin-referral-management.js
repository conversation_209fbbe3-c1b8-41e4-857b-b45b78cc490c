// Admin Referral Management Tools
// Comprehensive admin tools for commission audit trails, relationship tracking, and verification
// Date: 2025-01-10

const { db } = require('../src/database/supabase-client');

/**
 * Handle admin referral management dashboard
 */
async function handleAdminReferralManagement(ctx) {
  try {
    console.log(`🔧 [ADMIN_REFERRAL] Loading admin referral management for ${ctx.from.username}`);

    // Get system-wide referral statistics
    const systemStats = await getSystemReferralStats();

    let managementMessage = `🔧 **ADMIN REFERRAL MANAGEMENT**\n\n`;

    // System Overview
    managementMessage += `📊 **SYSTEM OVERVIEW**\n`;
    managementMessage += `• **Total Referrals:** ${systemStats.totalReferrals}\n`;
    managementMessage += `• **Active Referrals:** ${systemStats.activeReferrals}\n`;
    managementMessage += `• **Total Commission Paid:** $${systemStats.totalCommissionPaid.toFixed(2)}\n`;
    managementMessage += `• **Pending Commission:** $${systemStats.pendingCommission.toFixed(2)}\n`;
    managementMessage += `• **Top Referrers:** ${systemStats.topReferrers}\n`;
    managementMessage += `• **Commission Rate:** ${systemStats.avgCommissionRate.toFixed(1)}%\n\n`;

    // Recent Activity
    if (systemStats.recentActivity.length > 0) {
      managementMessage += `🔔 **RECENT ACTIVITY (Last 24 Hours)**\n`;
      systemStats.recentActivity.slice(0, 5).forEach(activity => {
        managementMessage += `• ${activity.description}\n`;
      });
      managementMessage += `\n`;
    }

    // Alerts and Issues
    const alerts = await getSystemAlerts();
    if (alerts.length > 0) {
      managementMessage += `⚠️ **SYSTEM ALERTS**\n`;
      alerts.forEach(alert => {
        managementMessage += `• ${alert.icon} ${alert.message}\n`;
      });
      managementMessage += `\n`;
    }

    const keyboard = [
      [
        { text: "👥 User Commission Audit", callback_data: "admin_user_commission_audit" },
        { text: "🔍 Referral Relationship Tracker", callback_data: "admin_referral_tracker" }
      ],
      [
        { text: "📊 Commission Verification", callback_data: "admin_commission_verification" },
        { text: "📈 Performance Reports", callback_data: "admin_referral_reports" }
      ],
      [
        { text: "🔧 Bulk Commission Adjustments", callback_data: "admin_bulk_adjustments" },
        { text: "🌐 Network Visualization", callback_data: "admin_network_visualization" }
      ],
      [
        { text: "📋 Audit Logs", callback_data: "admin_commission_audit_logs" },
        { text: "⚙️ System Settings", callback_data: "admin_referral_settings" }
      ],
      [
        { text: "🔄 Refresh Dashboard", callback_data: "admin_referral_management" },
        { text: "🔙 Back to Admin Panel", callback_data: "admin_panel" }
      ]
    ];

    await ctx.replyWithMarkdown(managementMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Admin referral management error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading admin referral management**\n\nPlease try again.');
  }
}

/**
 * Handle user commission audit
 */
async function handleUserCommissionAudit(ctx) {
  try {
    let auditMessage = `👥 **USER COMMISSION AUDIT**\n\n`;
    auditMessage += `Select an option to audit user commissions:\n\n`;

    const keyboard = [
      [
        { text: "🔍 Search by Username", callback_data: "audit_search_username" },
        { text: "📊 Top Earners Audit", callback_data: "audit_top_earners" }
      ],
      [
        { text: "⚠️ Suspicious Activity", callback_data: "audit_suspicious_activity" },
        { text: "📈 Recent Commission Changes", callback_data: "audit_recent_changes" }
      ],
      [
        { text: "💰 High Value Transactions", callback_data: "audit_high_value" },
        { text: "🔄 Bulk Verification", callback_data: "audit_bulk_verification" }
      ],
      [
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    await ctx.replyWithMarkdown(auditMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('User commission audit error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading commission audit**\n\nPlease try again.');
  }
}

/**
 * Handle referral relationship tracker
 */
async function handleReferralTracker(ctx) {
  try {
    // Get referral network statistics
    const networkStats = await getReferralNetworkStats();

    let trackerMessage = `🔍 **REFERRAL RELATIONSHIP TRACKER**\n\n`;

    // Network Overview
    trackerMessage += `🌐 **NETWORK OVERVIEW**\n`;
    trackerMessage += `• **Total Relationships:** ${networkStats.totalRelationships}\n`;
    trackerMessage += `• **Active Relationships:** ${networkStats.activeRelationships}\n`;
    trackerMessage += `• **Orphaned Users:** ${networkStats.orphanedUsers}\n`;
    trackerMessage += `• **Multi-level Depth:** ${networkStats.maxDepth} levels\n`;
    trackerMessage += `• **Average Network Size:** ${networkStats.avgNetworkSize.toFixed(1)} referrals\n\n`;

    // Top Networks
    if (networkStats.topNetworks.length > 0) {
      trackerMessage += `🏆 **TOP REFERRAL NETWORKS**\n`;
      networkStats.topNetworks.forEach((network, index) => {
        trackerMessage += `${index + 1}. **${network.username}**: ${network.networkSize} referrals\n`;
      });
      trackerMessage += `\n`;
    }

    const keyboard = [
      [
        { text: "🔍 Search Network", callback_data: "tracker_search_network" },
        { text: "👤 User Relationships", callback_data: "tracker_user_relationships" }
      ],
      [
        { text: "🌳 Network Tree View", callback_data: "tracker_network_tree" },
        { text: "📊 Relationship Analytics", callback_data: "tracker_relationship_analytics" }
      ],
      [
        { text: "⚠️ Broken Relationships", callback_data: "tracker_broken_relationships" },
        { text: "🔧 Fix Orphaned Users", callback_data: "tracker_fix_orphaned" }
      ],
      [
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    await ctx.replyWithMarkdown(trackerMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Referral tracker error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading referral tracker**\n\nPlease try again.');
  }
}

/**
 * Handle commission verification
 */
async function handleCommissionVerification(ctx) {
  try {
    // Get verification statistics
    const verificationStats = await getCommissionVerificationStats();

    let verificationMessage = `📊 **COMMISSION VERIFICATION**\n\n`;

    // Verification Overview
    verificationMessage += `🔍 **VERIFICATION OVERVIEW**\n`;
    verificationMessage += `• **Total Transactions:** ${verificationStats.totalTransactions}\n`;
    verificationMessage += `• **Verified Transactions:** ${verificationStats.verifiedTransactions}\n`;
    verificationMessage += `• **Pending Verification:** ${verificationStats.pendingVerification}\n`;
    verificationMessage += `• **Failed Verifications:** ${verificationStats.failedVerifications}\n`;
    verificationMessage += `• **Accuracy Rate:** ${verificationStats.accuracyRate.toFixed(1)}%\n\n`;

    // Recent Verification Issues
    if (verificationStats.recentIssues.length > 0) {
      verificationMessage += `⚠️ **RECENT VERIFICATION ISSUES**\n`;
      verificationStats.recentIssues.forEach(issue => {
        verificationMessage += `• ${issue.description}\n`;
      });
      verificationMessage += `\n`;
    }

    const keyboard = [
      [
        { text: "🔍 Verify Specific Transaction", callback_data: "verify_specific_transaction" },
        { text: "📊 Batch Verification", callback_data: "verify_batch_transactions" }
      ],
      [
        { text: "⚠️ Review Failed Verifications", callback_data: "verify_review_failed" },
        { text: "🔧 Recalculate Commissions", callback_data: "verify_recalculate" }
      ],
      [
        { text: "📈 Verification Reports", callback_data: "verify_reports" },
        { text: "⚙️ Verification Settings", callback_data: "verify_settings" }
      ],
      [
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    await ctx.replyWithMarkdown(verificationMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Commission verification error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading commission verification**\n\nPlease try again.');
  }
}

/**
 * Get system-wide referral statistics
 */
async function getSystemReferralStats() {
  try {
    // Get total referrals
    const { data: referrals, error: referralsError } = await db.client
      .from('referrals')
      .select('id, status, created_at');

    const totalReferrals = referrals ? referrals.length : 0;
    const activeReferrals = referrals ? referrals.filter(r => r.status === 'active').length : 0;

    // Get commission statistics
    const { data: commissions, error: commissionsError } = await db.client
      .from('commission_transactions')
      .select('usdt_commission, share_commission, commission_rate, payment_date, status');

    const totalCommissionPaid = commissions ? 
      commissions
        .filter(c => c.status === 'approved')
        .reduce((sum, c) => sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0) : 0;

    const pendingCommission = commissions ?
      commissions
        .filter(c => c.status === 'pending')
        .reduce((sum, c) => sum + parseFloat(c.usdt_commission || 0) + parseFloat(c.share_commission || 0), 0) : 0;

    const avgCommissionRate = commissions && commissions.length > 0 ?
      commissions.reduce((sum, c) => sum + parseFloat(c.commission_rate || 0), 0) / commissions.length : 0;

    // Get top referrers count
    const { data: topReferrersData, error: topReferrersError } = await db.client
      .from('referrals')
      .select('referrer_id')
      .eq('status', 'active');

    const referrerCounts = {};
    if (topReferrersData) {
      topReferrersData.forEach(ref => {
        referrerCounts[ref.referrer_id] = (referrerCounts[ref.referrer_id] || 0) + 1;
      });
    }
    const topReferrers = Object.keys(referrerCounts).length;

    // Get recent activity
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentActivity = [];

    if (commissions) {
      const recentCommissions = commissions.filter(c => new Date(c.payment_date) >= twentyFourHoursAgo);
      recentCommissions.forEach(commission => {
        recentActivity.push({
          description: `💰 Commission earned: $${(parseFloat(commission.usdt_commission || 0) + parseFloat(commission.share_commission || 0)).toFixed(2)}`
        });
      });
    }

    if (referrals) {
      const recentReferrals = referrals.filter(r => new Date(r.created_at) >= twentyFourHoursAgo);
      recentReferrals.forEach(() => {
        recentActivity.push({
          description: `👥 New referral registered`
        });
      });
    }

    return {
      totalReferrals,
      activeReferrals,
      totalCommissionPaid,
      pendingCommission,
      topReferrers,
      avgCommissionRate,
      recentActivity: recentActivity.slice(0, 10)
    };

  } catch (error) {
    console.error('Error getting system referral stats:', error);
    return {
      totalReferrals: 0,
      activeReferrals: 0,
      totalCommissionPaid: 0,
      pendingCommission: 0,
      topReferrers: 0,
      avgCommissionRate: 0,
      recentActivity: []
    };
  }
}

/**
 * Get system alerts for referral issues
 */
async function getSystemAlerts() {
  const alerts = [];

  try {
    // Check for orphaned users (users without referrers)
    const { data: orphanedUsers, error: orphanedError } = await db.client
      .from('users')
      .select('id')
      .not('id', 'in', `(SELECT referred_id FROM referrals WHERE status = 'active')`);

    if (orphanedUsers && orphanedUsers.length > 10) {
      alerts.push({
        icon: '⚠️',
        message: `${orphanedUsers.length} users without active referrers`
      });
    }

    // Check for failed commission calculations
    const { data: failedCommissions, error: failedError } = await db.client
      .from('commission_transactions')
      .select('id')
      .eq('status', 'failed');

    if (failedCommissions && failedCommissions.length > 0) {
      alerts.push({
        icon: '❌',
        message: `${failedCommissions.length} failed commission transactions need review`
      });
    }

    // Check for high-value pending commissions
    const { data: highValuePending, error: highValueError } = await db.client
      .from('commission_transactions')
      .select('usdt_commission, share_commission')
      .eq('status', 'pending')
      .gte('usdt_commission', 1000);

    if (highValuePending && highValuePending.length > 0) {
      alerts.push({
        icon: '💰',
        message: `${highValuePending.length} high-value commissions pending approval`
      });
    }

  } catch (error) {
    console.error('Error getting system alerts:', error);
  }

  return alerts;
}

/**
 * Get referral network statistics
 */
async function getReferralNetworkStats() {
  try {
    // Get all referral relationships
    const { data: relationships, error: relationshipsError } = await db.client
      .from('referrals')
      .select('referrer_id, referred_id, status');

    const totalRelationships = relationships ? relationships.length : 0;
    const activeRelationships = relationships ? relationships.filter(r => r.status === 'active').length : 0;

    // Calculate network sizes
    const networkSizes = {};
    if (relationships) {
      relationships.forEach(rel => {
        if (rel.status === 'active') {
          networkSizes[rel.referrer_id] = (networkSizes[rel.referrer_id] || 0) + 1;
        }
      });
    }

    const avgNetworkSize = Object.keys(networkSizes).length > 0 ?
      Object.values(networkSizes).reduce((sum, size) => sum + size, 0) / Object.keys(networkSizes).length : 0;

    // Get orphaned users
    const { data: allUsers, error: usersError } = await db.client
      .from('users')
      .select('id');

    const referredUserIds = relationships ? relationships.map(r => r.referred_id) : [];
    const orphanedUsers = allUsers ? allUsers.filter(u => !referredUserIds.includes(u.id)).length : 0;

    // Get top networks
    const topNetworks = Object.entries(networkSizes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([userId, size]) => ({ userId, networkSize: size, username: `User ${userId}` }));

    return {
      totalRelationships,
      activeRelationships,
      orphanedUsers,
      maxDepth: 3, // Placeholder - would need recursive query to calculate actual depth
      avgNetworkSize,
      topNetworks
    };

  } catch (error) {
    console.error('Error getting referral network stats:', error);
    return {
      totalRelationships: 0,
      activeRelationships: 0,
      orphanedUsers: 0,
      maxDepth: 0,
      avgNetworkSize: 0,
      topNetworks: []
    };
  }
}

/**
 * Get commission verification statistics
 */
async function getCommissionVerificationStats() {
  try {
    // Get all commission transactions
    const { data: transactions, error: transactionsError } = await db.client
      .from('commission_transactions')
      .select('id, status, created_at, usdt_commission, share_commission');

    const totalTransactions = transactions ? transactions.length : 0;
    const verifiedTransactions = transactions ? transactions.filter(t => t.status === 'approved').length : 0;
    const pendingVerification = transactions ? transactions.filter(t => t.status === 'pending').length : 0;
    const failedVerifications = transactions ? transactions.filter(t => t.status === 'failed').length : 0;

    const accuracyRate = totalTransactions > 0 ? (verifiedTransactions / totalTransactions) * 100 : 0;

    // Get recent issues (placeholder)
    const recentIssues = [];
    if (failedVerifications > 0) {
      recentIssues.push({
        description: `${failedVerifications} transactions failed verification`
      });
    }

    return {
      totalTransactions,
      verifiedTransactions,
      pendingVerification,
      failedVerifications,
      accuracyRate,
      recentIssues
    };

  } catch (error) {
    console.error('Error getting commission verification stats:', error);
    return {
      totalTransactions: 0,
      verifiedTransactions: 0,
      pendingVerification: 0,
      failedVerifications: 0,
      accuracyRate: 0,
      recentIssues: []
    };
  }
}

/**
 * Handle bulk commission adjustments
 */
async function handleBulkCommissionAdjustments(ctx) {
  try {
    let adjustmentMessage = `🔧 **BULK COMMISSION ADJUSTMENTS**\n\n`;
    adjustmentMessage += `⚠️ **WARNING**: Bulk adjustments affect multiple users and should be used carefully.\n\n`;
    adjustmentMessage += `Select an adjustment type:\n\n`;

    const keyboard = [
      [
        { text: "💰 Bonus Commission Distribution", callback_data: "bulk_bonus_distribution" },
        { text: "🔧 Rate Adjustment", callback_data: "bulk_rate_adjustment" }
      ],
      [
        { text: "❌ Reverse Commissions", callback_data: "bulk_reverse_commissions" },
        { text: "🔄 Recalculate Period", callback_data: "bulk_recalculate_period" }
      ],
      [
        { text: "📊 Preview Changes", callback_data: "bulk_preview_changes" },
        { text: "📋 Adjustment History", callback_data: "bulk_adjustment_history" }
      ],
      [
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    await ctx.replyWithMarkdown(adjustmentMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Bulk commission adjustments error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading bulk adjustments**\n\nPlease try again.');
  }
}

/**
 * Handle network visualization
 */
async function handleNetworkVisualization(ctx) {
  try {
    // Get network visualization data
    const networkData = await getNetworkVisualizationData();

    let visualizationMessage = `🌐 **REFERRAL NETWORK VISUALIZATION**\n\n`;

    // Network Summary
    visualizationMessage += `📊 **NETWORK SUMMARY**\n`;
    visualizationMessage += `• **Total Nodes:** ${networkData.totalNodes}\n`;
    visualizationMessage += `• **Total Connections:** ${networkData.totalConnections}\n`;
    visualizationMessage += `• **Network Density:** ${networkData.density.toFixed(2)}%\n`;
    visualizationMessage += `• **Largest Network:** ${networkData.largestNetwork} nodes\n`;
    visualizationMessage += `• **Average Depth:** ${networkData.averageDepth.toFixed(1)} levels\n\n`;

    // Top Networks
    if (networkData.topNetworks.length > 0) {
      visualizationMessage += `🏆 **TOP NETWORKS BY SIZE**\n`;
      networkData.topNetworks.forEach((network, index) => {
        visualizationMessage += `${index + 1}. **${network.rootUser}**: ${network.size} nodes, ${network.depth} levels\n`;
      });
      visualizationMessage += `\n`;
    }

    // Network Health Indicators
    visualizationMessage += `🔍 **NETWORK HEALTH**\n`;
    visualizationMessage += `• **Active Connections:** ${networkData.activeConnections}%\n`;
    visualizationMessage += `• **Orphaned Nodes:** ${networkData.orphanedNodes}\n`;
    visualizationMessage += `• **Broken Chains:** ${networkData.brokenChains}\n`;
    visualizationMessage += `• **Commission Flow:** ${networkData.commissionFlow}%\n\n`;

    const keyboard = [
      [
        { text: "🌳 View Network Tree", callback_data: "view_network_tree" },
        { text: "📊 Network Analytics", callback_data: "network_analytics" }
      ],
      [
        { text: "🔍 Search Network Path", callback_data: "search_network_path" },
        { text: "📈 Growth Patterns", callback_data: "network_growth_patterns" }
      ],
      [
        { text: "🔧 Optimize Network", callback_data: "optimize_network" },
        { text: "📋 Export Network Data", callback_data: "export_network_data" }
      ],
      [
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    await ctx.replyWithMarkdown(visualizationMessage, {
      reply_markup: { inline_keyboard: keyboard }
    });

  } catch (error) {
    console.error('Network visualization error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading network visualization**\n\nPlease try again.');
  }
}

/**
 * Handle commission audit logs
 */
async function handleCommissionAuditLogs(ctx) {
  try {
    // Get recent audit logs
    const auditLogs = await getRecentAuditLogs();

    let logsMessage = `📋 **COMMISSION AUDIT LOGS**\n\n`;

    if (auditLogs.length > 0) {
      logsMessage += `**Recent Audit Events (Last 50):**\n\n`;

      auditLogs.forEach((log, index) => {
        const timestamp = new Date(log.created_at).toLocaleString();
        const actionIcon = getActionIcon(log.action_type);

        logsMessage += `**${index + 1}.** ${actionIcon} **${log.action_type.toUpperCase()}**\n`;
        logsMessage += `   📅 ${timestamp}\n`;
        logsMessage += `   👤 User: ${log.performed_by || 'System'}\n`;

        if (log.change_reason) {
          logsMessage += `   📝 Reason: ${log.change_reason}\n`;
        }

        if (log.old_values && Object.keys(log.old_values).length > 0) {
          logsMessage += `   📊 Changes: ${JSON.stringify(log.old_values)} → ${JSON.stringify(log.new_values)}\n`;
        }

        logsMessage += `\n`;
      });
    } else {
      logsMessage += `📝 **No audit logs found**\n\nAudit logs will appear here as commission transactions are processed.\n\n`;
    }

    const keyboard = [
      [
        { text: "🔍 Search Logs", callback_data: "search_audit_logs" },
        { text: "📊 Log Analytics", callback_data: "audit_log_analytics" }
      ],
      [
        { text: "📅 Filter by Date", callback_data: "filter_logs_date" },
        { text: "👤 Filter by User", callback_data: "filter_logs_user" }
      ],
      [
        { text: "📋 Export Logs", callback_data: "export_audit_logs" },
        { text: "⚙️ Log Settings", callback_data: "audit_log_settings" }
      ],
      [
        { text: "🔄 Refresh Logs", callback_data: "admin_commission_audit_logs" },
        { text: "🔙 Back to Management", callback_data: "admin_referral_management" }
      ]
    ];

    try {
      await ctx.replyWithMarkdown(logsMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    } catch (markdownError) {
      console.error('❌ Markdown parsing error in audit logs:', markdownError);

      // Fallback: Send without markdown formatting
      const plainMessage = logsMessage.replace(/\*\*/g, '').replace(/`/g, '');
      await ctx.reply(plainMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });
    }

  } catch (error) {
    console.error('Commission audit logs error:', error);
    await ctx.replyWithMarkdown('❌ **Error loading audit logs**\n\nPlease try again.');
  }
}

/**
 * Get network visualization data
 */
async function getNetworkVisualizationData() {
  try {
    // Get all referral relationships
    const { data: relationships, error: relationshipsError } = await db.client
      .from('referrals')
      .select('referrer_id, referred_id, status, created_at');

    if (relationshipsError || !relationships) {
      return getEmptyNetworkData();
    }

    const totalNodes = new Set([...relationships.map(r => r.referrer_id), ...relationships.map(r => r.referred_id)]).size;
    const totalConnections = relationships.length;
    const activeConnections = relationships.filter(r => r.status === 'active').length;
    const activeConnectionsPercent = totalConnections > 0 ? (activeConnections / totalConnections) * 100 : 0;

    // Calculate network density (simplified)
    const maxPossibleConnections = totalNodes * (totalNodes - 1) / 2;
    const density = maxPossibleConnections > 0 ? (totalConnections / maxPossibleConnections) * 100 : 0;

    // Find largest network
    const networkSizes = {};
    relationships.forEach(rel => {
      if (rel.status === 'active') {
        networkSizes[rel.referrer_id] = (networkSizes[rel.referrer_id] || 0) + 1;
      }
    });

    const largestNetwork = Object.keys(networkSizes).length > 0 ? Math.max(...Object.values(networkSizes)) : 0;

    // Get top networks
    const topNetworks = Object.entries(networkSizes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([userId, size]) => ({
        rootUser: `User ${userId}`,
        size,
        depth: 2 // Simplified - would need recursive calculation for actual depth
      }));

    // Calculate orphaned nodes
    const referredIds = relationships.map(r => r.referred_id);
    const { data: allUsers, error: usersError } = await db.client
      .from('users')
      .select('id');

    const orphanedNodes = allUsers ? allUsers.filter(u => !referredIds.includes(u.id)).length : 0;

    return {
      totalNodes,
      totalConnections,
      density,
      largestNetwork,
      averageDepth: 2.5, // Simplified calculation
      topNetworks,
      activeConnections: activeConnectionsPercent,
      orphanedNodes,
      brokenChains: 0, // Would need complex calculation
      commissionFlow: 85 // Placeholder - would calculate based on actual commission flow
    };

  } catch (error) {
    console.error('Error getting network visualization data:', error);
    return getEmptyNetworkData();
  }
}

/**
 * Get empty network data structure
 */
function getEmptyNetworkData() {
  return {
    totalNodes: 0,
    totalConnections: 0,
    density: 0,
    largestNetwork: 0,
    averageDepth: 0,
    topNetworks: [],
    activeConnections: 0,
    orphanedNodes: 0,
    brokenChains: 0,
    commissionFlow: 0
  };
}

/**
 * Get recent audit logs
 */
async function getRecentAuditLogs() {
  try {
    const { data: logs, error: logsError } = await db.client
      .from('commission_audit_log')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50);

    return logs || [];

  } catch (error) {
    console.error('Error getting audit logs:', error);
    return [];
  }
}

/**
 * Get action icon for audit log action type
 */
function getActionIcon(actionType) {
  const icons = {
    'created': '✅',
    'modified': '✏️',
    'approved': '👍',
    'rejected': '❌',
    'adjusted': '🔧',
    'deleted': '🗑️',
    'reversed': '↩️'
  };
  return icons[actionType] || '📝';
}

module.exports = {
  handleAdminReferralManagement,
  handleUserCommissionAudit,
  handleReferralTracker,
  handleCommissionVerification,
  handleBulkCommissionAdjustments,
  handleNetworkVisualization,
  handleCommissionAuditLogs,
  getSystemReferralStats,
  getSystemAlerts,
  getReferralNetworkStats,
  getCommissionVerificationStats,
  getNetworkVisualizationData,
  getRecentAuditLogs,
  getActionIcon
};
