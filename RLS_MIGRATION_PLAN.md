# 🔒 RLS COMPLIANCE MIGRATION PLAN FOR TELEGRAM BOT

## 📋 EXECUTIVE SUMMARY

The Telegram bot currently uses SERVICE ROLE access that bypasses all Row Level Security (RLS) policies, causing data integrity issues and security vulnerabilities. This plan outlines the migration to RLS-compliant access.

## 🚨 CRITICAL ISSUES IDENTIFIED

### **Current Problems:**
1. **Service Role Bypass**: <PERSON><PERSON> ignores all RLS policies
2. **Data Integrity Violations**: <PERSON><PERSON> creates records that violate website constraints
3. **Security Vulnerabilities**: No user isolation between Telegram users
4. **Constraint Violations**: KYC and payment data fails due to RLS mismatches

### **RLS Policy Analysis:**
- **Users Table**: Requires `auth_user_id = auth.uid()` for user operations
- **Telegram Users**: Service role has full access, users can only access own data
- **KYC Information**: Requires authenticated role
- **Commission Balances**: Users can only access own balances
- **Payment Transactions**: Users can only access own transactions
- **Admin Operations**: Require admin role verification

## 🔧 IMPLEMENTATION STRATEGY

### **Phase 1: Infrastructure Setup** ✅
- [x] Create RLS-compliant database client (`src/database/rls-compliant-client.js`)
- [x] Implement user context management
- [x] Add access validation methods
- [x] Maintain backward compatibility

### **Phase 2: Critical Operations Migration** (NEXT)
1. **User Authentication & Registration**
   - Migrate `authenticateUser()` function
   - Ensure proper user context setting
   - Fix telegram_users creation with RLS compliance

2. **KYC System**
   - Fix constraint violations in `kyc_information` table
   - Implement proper user context for KYC operations
   - Add RLS-compliant KYC data access

3. **Payment System**
   - Migrate payment transaction operations
   - Ensure user isolation for payment data
   - Fix commission balance access

### **Phase 3: Admin Operations** (LATER)
- Implement admin role verification
- Migrate admin functions to use proper permissions
- Add audit logging for admin operations

### **Phase 4: Testing & Validation** (FINAL)
- Comprehensive testing of all bot operations
- Verify no data leakage between users
- Performance testing with RLS enabled
- Security audit of bot access patterns

## 🔒 SPECIFIC RLS REQUIREMENTS

### **User Context Requirements:**
```javascript
// Each bot operation must set proper user context
await rlsDb.setUserContext(telegramId, userId);

// Operations must validate user access
if (!rlsDb.validateUserAccess(targetUserId, 'read')) {
  throw new Error('Access denied');
}
```

### **Table-Specific Requirements:**

#### **users table:**
- Requires `auth_user_id = auth.uid()` for user operations
- Service role can manage all users (admin operations)
- Bot needs to create auth users for Telegram users

#### **telegram_users table:**
- Service role has full access (current bot usage OK)
- Users can only access own telegram data
- Bot operations are compliant

#### **kyc_information table:**
- Requires `auth.role() = 'authenticated'`
- Bot needs to authenticate users before KYC operations
- Fix constraint violations (id_type check)

#### **commission_balances table:**
- Users can only access own balances
- Service role can manage all balances
- Bot needs user context for balance queries

#### **crypto_payment_transactions table:**
- Users can only access own transactions
- Service role can manage all transactions
- Bot operations need user context

## 🚨 IMMEDIATE ACTIONS REQUIRED

### **1. Fix KYC Constraint Violation** (CRITICAL)
```sql
-- Check current constraint
SELECT conname, consrc FROM pg_constraint 
WHERE conname = 'kyc_information_id_type_check';

-- Fix constraint to allow 'OTH' value
ALTER TABLE kyc_information 
DROP CONSTRAINT IF EXISTS kyc_information_id_type_check;

ALTER TABLE kyc_information 
ADD CONSTRAINT kyc_information_id_type_check 
CHECK (id_type IN ('passport', 'national_id', 'drivers_license', 'OTH'));
```

### **2. Create Auth Users for Telegram Users** (HIGH PRIORITY)
```javascript
// For each Telegram user without auth_user_id
const authUser = await supabase.auth.admin.createUser({
  email: `telegram_${telegramId}@aureus.local`,
  password: generateSecurePassword(),
  email_confirm: true,
  user_metadata: {
    telegram_id: telegramId,
    source: 'telegram_bot'
  }
});

// Update users table with auth_user_id
await supabase
  .from('users')
  .update({ auth_user_id: authUser.user.id })
  .eq('id', userId);
```

### **3. Migrate Critical Bot Functions** (HIGH PRIORITY)
- `authenticateUser()` - Set proper user context
- `handleKYCSubmission()` - Use authenticated operations
- `getCommissionBalance()` - Validate user access
- `createPaymentTransaction()` - Ensure user isolation

## 📊 MIGRATION TIMELINE

### **Week 1: Critical Fixes**
- [ ] Fix KYC constraint violation
- [ ] Implement user context in authentication
- [ ] Migrate user registration flow

### **Week 2: Core Operations**
- [ ] Migrate KYC system to RLS compliance
- [ ] Fix payment transaction access
- [ ] Implement commission balance validation

### **Week 3: Admin Functions**
- [ ] Migrate admin operations
- [ ] Add proper role verification
- [ ] Implement audit logging

### **Week 4: Testing & Deployment**
- [ ] Comprehensive testing
- [ ] Security audit
- [ ] Performance validation
- [ ] Production deployment

## 🔍 TESTING REQUIREMENTS

### **Security Tests:**
- Verify users can only access own data
- Test admin operations require proper permissions
- Validate no data leakage between users

### **Functionality Tests:**
- All bot operations work with RLS enabled
- User registration creates proper auth users
- KYC submissions work without constraint violations
- Payment system maintains user isolation

### **Performance Tests:**
- RLS policies don't significantly impact performance
- Database queries are optimized for RLS
- Bot response times remain acceptable

## 🎯 SUCCESS CRITERIA

1. **Security**: All bot operations respect RLS policies
2. **Functionality**: No loss of bot features or capabilities
3. **Data Integrity**: No constraint violations or data corruption
4. **Performance**: Minimal impact on bot response times
5. **Compliance**: Bot and website have consistent security model

## 📞 NEXT STEPS

1. **Immediate**: Fix KYC constraint violation
2. **Priority 1**: Implement user context in authentication
3. **Priority 2**: Migrate critical operations to RLS compliance
4. **Priority 3**: Comprehensive testing and validation

This migration will ensure the Telegram bot operates with the same security standards as the website while maintaining all current functionality.
