/**
 * Aureus Alliance Holdings Mobile App
 * Portfolio Context Provider
 * 
 * Manages user portfolio data, share purchases, and commission balances
 */

import React, {createContext, useContext, useEffect, useReducer} from 'react';
import {dbService, realtimeService} from '../services/supabaseClient';
import {useAuth} from './AuthContext';

// Initial state
const initialState = {
  shares: [],
  commissions: [],
  totalShares: 0,
  totalValue: 0,
  usdtBalance: 0,
  shareCommissionBalance: 0,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Action types
const PORTFOLIO_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_SHARES: 'SET_SHARES',
  SET_COMMISSIONS: 'SET_COMMISSIONS',
  SET_PORTFOLIO_DATA: 'SET_PORTFOLIO_DATA',
  ADD_SHARE_PURCHASE: 'ADD_SHARE_PURCHASE',
  UPDATE_SHARE_PURCHASE: 'UPDATE_SHARE_PURCHASE',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  RESET_PORTFOLIO: 'RESET_PORTFOLIO',
};

// Reducer function
const portfolioReducer = (state, action) => {
  switch (action.type) {
    case PORTFOLIO_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case PORTFOLIO_ACTIONS.SET_SHARES:
      const shares = action.payload || [];
      const totalShares = shares.reduce((sum, share) => sum + (share.shares_purchased || 0), 0);
      const totalValue = shares.reduce((sum, share) => sum + (share.total_amount || 0), 0);
      
      return {
        ...state,
        shares,
        totalShares,
        totalValue,
        lastUpdated: new Date().toISOString(),
        error: null,
      };

    case PORTFOLIO_ACTIONS.SET_COMMISSIONS:
      const commissions = action.payload || [];
      const usdtBalance = commissions.reduce((sum, comm) => sum + (comm.usdt_balance || 0), 0);
      const shareCommissionBalance = commissions.reduce((sum, comm) => sum + (comm.share_balance || 0), 0);
      
      return {
        ...state,
        commissions,
        usdtBalance,
        shareCommissionBalance,
        lastUpdated: new Date().toISOString(),
        error: null,
      };

    case PORTFOLIO_ACTIONS.SET_PORTFOLIO_DATA:
      const {shares: newShares, commissions: newCommissions} = action.payload;
      
      const calculatedTotalShares = newShares.reduce((sum, share) => sum + (share.shares_purchased || 0), 0);
      const calculatedTotalValue = newShares.reduce((sum, share) => sum + (share.total_amount || 0), 0);
      const calculatedUsdtBalance = newCommissions.reduce((sum, comm) => sum + (comm.usdt_balance || 0), 0);
      const calculatedShareCommissionBalance = newCommissions.reduce((sum, comm) => sum + (comm.share_balance || 0), 0);
      
      return {
        ...state,
        shares: newShares,
        commissions: newCommissions,
        totalShares: calculatedTotalShares,
        totalValue: calculatedTotalValue,
        usdtBalance: calculatedUsdtBalance,
        shareCommissionBalance: calculatedShareCommissionBalance,
        lastUpdated: new Date().toISOString(),
        isLoading: false,
        error: null,
      };

    case PORTFOLIO_ACTIONS.ADD_SHARE_PURCHASE:
      const newPurchase = action.payload;
      const updatedShares = [newPurchase, ...state.shares];
      const newTotalShares = state.totalShares + (newPurchase.shares_purchased || 0);
      const newTotalValue = state.totalValue + (newPurchase.total_amount || 0);
      
      return {
        ...state,
        shares: updatedShares,
        totalShares: newTotalShares,
        totalValue: newTotalValue,
        lastUpdated: new Date().toISOString(),
      };

    case PORTFOLIO_ACTIONS.UPDATE_SHARE_PURCHASE:
      const {id, updates} = action.payload;
      const updatedSharesList = state.shares.map(share =>
        share.id === id ? {...share, ...updates} : share
      );
      
      return {
        ...state,
        shares: updatedSharesList,
        lastUpdated: new Date().toISOString(),
      };

    case PORTFOLIO_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case PORTFOLIO_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case PORTFOLIO_ACTIONS.RESET_PORTFOLIO:
      return initialState;

    default:
      return state;
  }
};

// Create context
const PortfolioContext = createContext();

// Portfolio provider component
export const PortfolioProvider = ({children}) => {
  const [state, dispatch] = useReducer(portfolioReducer, initialState);
  const {user, isAuthenticated} = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadPortfolioData();
      setupRealtimeSubscriptions();
    } else {
      dispatch({type: PORTFOLIO_ACTIONS.RESET_PORTFOLIO});
    }

    return () => {
      // Cleanup subscriptions when component unmounts or user changes
      cleanupSubscriptions();
    };
  }, [isAuthenticated, user]);

  let portfolioSubscription = null;
  let commissionSubscription = null;

  const loadPortfolioData = async () => {
    try {
      dispatch({type: PORTFOLIO_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: PORTFOLIO_ACTIONS.CLEAR_ERROR});

      const result = await dbService.getUserPortfolio(user.id);
      
      if (result.success) {
        dispatch({
          type: PORTFOLIO_ACTIONS.SET_PORTFOLIO_DATA,
          payload: result.data,
        });
      } else {
        dispatch({type: PORTFOLIO_ACTIONS.SET_ERROR, payload: result.error});
      }
    } catch (error) {
      console.error('Error loading portfolio data:', error);
      dispatch({type: PORTFOLIO_ACTIONS.SET_ERROR, payload: error.message});
    }
  };

  const setupRealtimeSubscriptions = () => {
    if (!user) return;

    // Subscribe to portfolio changes
    portfolioSubscription = realtimeService.subscribeToPortfolio(
      user.id,
      (payload) => {
        console.log('Portfolio change detected:', payload);
        // Reload portfolio data when changes occur
        loadPortfolioData();
      }
    );

    // Subscribe to commission changes
    commissionSubscription = realtimeService.subscribeToCommissions(
      user.id,
      (payload) => {
        console.log('Commission change detected:', payload);
        // Reload portfolio data when commission changes occur
        loadPortfolioData();
      }
    );
  };

  const cleanupSubscriptions = () => {
    if (portfolioSubscription) {
      realtimeService.unsubscribe(portfolioSubscription);
      portfolioSubscription = null;
    }
    if (commissionSubscription) {
      realtimeService.unsubscribe(commissionSubscription);
      commissionSubscription = null;
    }
  };

  const refreshPortfolio = async () => {
    await loadPortfolioData();
  };

  const createSharePurchase = async (purchaseData) => {
    try {
      dispatch({type: PORTFOLIO_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: PORTFOLIO_ACTIONS.CLEAR_ERROR});

      const result = await dbService.createSharePurchase(user.id, purchaseData);
      
      if (result.success) {
        dispatch({
          type: PORTFOLIO_ACTIONS.ADD_SHARE_PURCHASE,
          payload: result.data,
        });
        return {success: true, data: result.data};
      } else {
        dispatch({type: PORTFOLIO_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Failed to create share purchase';
      dispatch({type: PORTFOLIO_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    } finally {
      dispatch({type: PORTFOLIO_ACTIONS.SET_LOADING, payload: false});
    }
  };

  const updateSharePurchase = async (purchaseId, updates) => {
    try {
      dispatch({
        type: PORTFOLIO_ACTIONS.UPDATE_SHARE_PURCHASE,
        payload: {id: purchaseId, updates},
      });
      
      // Optionally sync with backend
      // await dbService.updateSharePurchase(purchaseId, updates);
      
      return {success: true};
    } catch (error) {
      const errorMessage = error.message || 'Failed to update share purchase';
      dispatch({type: PORTFOLIO_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const clearError = () => {
    dispatch({type: PORTFOLIO_ACTIONS.CLEAR_ERROR});
  };

  // Calculate derived values
  const getPortfolioSummary = () => {
    const currentSharePrice = 5.00; // This should come from investment_phases table
    const currentPortfolioValue = state.totalShares * currentSharePrice;
    const totalInvested = state.totalValue;
    const profitLoss = currentPortfolioValue - totalInvested;
    const profitLossPercentage = totalInvested > 0 ? (profitLoss / totalInvested) * 100 : 0;

    return {
      totalShares: state.totalShares,
      totalInvested,
      currentValue: currentPortfolioValue,
      profitLoss,
      profitLossPercentage,
      usdtBalance: state.usdtBalance,
      shareCommissionBalance: state.shareCommissionBalance,
    };
  };

  // Context value
  const value = {
    // State
    shares: state.shares,
    commissions: state.commissions,
    totalShares: state.totalShares,
    totalValue: state.totalValue,
    usdtBalance: state.usdtBalance,
    shareCommissionBalance: state.shareCommissionBalance,
    isLoading: state.isLoading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    
    // Actions
    loadPortfolioData,
    refreshPortfolio,
    createSharePurchase,
    updateSharePurchase,
    clearError,
    
    // Computed values
    getPortfolioSummary,
  };

  return (
    <PortfolioContext.Provider value={value}>
      {children}
    </PortfolioContext.Provider>
  );
};

// Custom hook to use portfolio context
export const usePortfolio = () => {
  const context = useContext(PortfolioContext);
  
  if (!context) {
    throw new Error('usePortfolio must be used within a PortfolioProvider');
  }
  
  return context;
};

export default PortfolioContext;
