/**
 * Aureus Alliance Holdings Mobile App
 * Forgot Password Screen - Password Reset
 */

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {TextInput, Button, Card, HelperText} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {colors, typography, spacing} from '../../constants/theme';

const ForgotPasswordScreen = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const {resetPassword, error: authError, clearError} = useAuth();

  const validateEmail = () => {
    const newErrors = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleResetPassword = async () => {
    if (!validateEmail()) {
      return;
    }

    setIsSubmitting(true);
    clearError();

    try {
      const result = await resetPassword(email.trim().toLowerCase());

      if (result.success) {
        setIsEmailSent(true);
        Alert.alert(
          'Reset Email Sent',
          'Please check your email for password reset instructions.',
          [{text: 'OK'}]
        );
      } else {
        Alert.alert(
          'Reset Failed',
          result.error || 'Unable to send reset email. Please try again.',
          [{text: 'OK'}]
        );
      }
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert(
        'Reset Error',
        'An unexpected error occurred. Please try again.',
        [{text: 'OK'}]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.goBack();
  };

  const handleResendEmail = () => {
    setIsEmailSent(false);
    handleResetPassword();
  };

  if (isEmailSent) {
    return (
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.successContainer}>
            <View style={styles.successIconContainer}>
              <Icon name="mark-email-read" size={80} color={colors.success} />
            </View>
            
            <Text style={styles.successTitle}>Check Your Email</Text>
            <Text style={styles.successMessage}>
              We've sent password reset instructions to:
            </Text>
            <Text style={styles.emailText}>{email}</Text>
            
            <Text style={styles.instructionsText}>
              Click the link in the email to reset your password. If you don't see the email, check your spam folder.
            </Text>

            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={handleResendEmail}
                style={styles.resendButton}
                labelStyle={styles.resendButtonLabel}>
                Resend Email
              </Button>
              
              <Button
                mode="contained"
                onPress={handleBackToLogin}
                style={styles.backButton}
                contentStyle={styles.backButtonContent}
                labelStyle={styles.backButtonLabel}>
                Back to Login
              </Button>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled">
        
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Icon name="lock-reset" size={60} color={colors.primary} />
          </View>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you instructions to reset your password.
          </Text>
        </View>

        {/* Reset Form */}
        <Card style={styles.formCard}>
          <Card.Content>
            {/* Email Input */}
            <View style={styles.inputContainer}>
              <TextInput
                label="Email Address"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                error={!!errors.email}
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                theme={{
                  colors: {
                    primary: colors.primary,
                    outline: errors.email ? colors.error : colors.border,
                  },
                }}
              />
              <HelperText type="error" visible={!!errors.email}>
                {errors.email}
              </HelperText>
            </View>

            {/* Reset Button */}
            <Button
              mode="contained"
              onPress={handleResetPassword}
              loading={isSubmitting}
              disabled={isSubmitting}
              style={styles.resetButton}
              contentStyle={styles.resetButtonContent}
              labelStyle={styles.resetButtonLabel}>
              {isSubmitting ? 'Sending...' : 'Send Reset Instructions'}
            </Button>

            {/* Auth Error Display */}
            {authError && (
              <View style={styles.errorContainer}>
                <Icon name="error" size={20} color={colors.error} />
                <Text style={styles.errorText}>{authError}</Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Back to Login */}
        <TouchableOpacity
          style={styles.backToLoginContainer}
          onPress={handleBackToLogin}>
          <Icon name="arrow-back" size={20} color={colors.primary} />
          <Text style={styles.backToLoginText}>Back to Login</Text>
        </TouchableOpacity>

        {/* Help Section */}
        <View style={styles.helpContainer}>
          <Text style={styles.helpTitle}>Need Help?</Text>
          <Text style={styles.helpText}>
            If you're having trouble resetting your password, please contact our support team.
          </Text>
          <TouchableOpacity style={styles.supportButton}>
            <Icon name="support-agent" size={20} color={colors.primary} />
            <Text style={styles.supportButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    marginTop: spacing.xl,
  },
  iconContainer: {
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: spacing.md,
  },
  formCard: {
    marginBottom: spacing.lg,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  input: {
    backgroundColor: colors.surface,
  },
  resetButton: {
    backgroundColor: colors.primary,
  },
  resetButtonContent: {
    paddingVertical: spacing.sm,
  },
  resetButtonLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.error + '10',
    padding: spacing.md,
    borderRadius: 8,
    marginTop: spacing.sm,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    color: colors.error,
    marginLeft: spacing.sm,
    flex: 1,
  },
  backToLoginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  backToLoginText: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  helpContainer: {
    alignItems: 'center',
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  helpTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  helpText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: spacing.md,
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  supportButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  // Success screen styles
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg,
  },
  successIconContainer: {
    marginBottom: spacing.xl,
  },
  successTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emailText: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    fontWeight: typography.fontWeight.semibold,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  instructionsText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: spacing.xl,
  },
  actionButtons: {
    width: '100%',
    gap: spacing.md,
  },
  resendButton: {
    borderColor: colors.primary,
  },
  resendButtonLabel: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  backButton: {
    backgroundColor: colors.primary,
  },
  backButtonContent: {
    paddingVertical: spacing.sm,
  },
  backButtonLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default ForgotPasswordScreen;
