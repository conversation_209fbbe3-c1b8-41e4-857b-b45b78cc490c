const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixRegistrationDataIntegrity() {
  console.log('🚨 FIXING CRITICAL REGISTRATION DATA INTEGRITY ISSUE');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Identify broken telegram_users records (missing user_id)
    console.log('📋 Step 1: Identifying broken telegram_users records...');
    
    const { data: brokenTelegramUsers, error: brokenError } = await supabase
      .from('telegram_users')
      .select('*')
      .is('user_id', null);
    
    if (brokenError) {
      console.error('❌ Error fetching broken telegram users:', brokenError);
      return;
    }
    
    console.log(`🔍 Found ${brokenTelegramUsers.length} telegram_users records missing user_id`);
    
    if (brokenTelegramUsers.length === 0) {
      console.log('✅ No broken records found - system integrity is good');
      return;
    }
    
    // Step 2: For each broken record, try to find or create corresponding user
    console.log('\n📋 Step 2: Fixing broken records...');
    
    let fixedCount = 0;
    let createdCount = 0;
    let errorCount = 0;
    
    for (const telegramUser of brokenTelegramUsers) {
      console.log(`\n🔧 Processing Telegram user: ${telegramUser.username} (${telegramUser.telegram_id})`);
      
      try {
        // Try to find existing user by username
        const { data: existingUser, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('username', telegramUser.username)
          .single();
        
        let userId = null;
        
        if (existingUser && !userError) {
          // Found existing user - link to it
          userId = existingUser.id;
          console.log(`  ✅ Found existing user: ${existingUser.username} (ID: ${userId})`);
        } else {
          // No existing user - create new one
          console.log(`  🆕 Creating new user record for: ${telegramUser.username}`);
          
          const { data: newUser, error: createError } = await supabase
            .from('users')
            .insert({
              username: telegramUser.username,
              email: `${telegramUser.username}@telegram.local`,
              password_hash: 'telegram_auth',
              full_name: `${telegramUser.first_name || ''} ${telegramUser.last_name || ''}`.trim() || telegramUser.username,
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();
          
          if (createError) {
            console.error(`  ❌ Error creating user for ${telegramUser.username}:`, createError);
            errorCount++;
            continue;
          }
          
          userId = newUser.id;
          createdCount++;
          console.log(`  ✅ Created new user: ${newUser.username} (ID: ${userId})`);
        }
        
        // Update telegram_users record with user_id
        const { error: updateError } = await supabase
          .from('telegram_users')
          .update({
            user_id: userId,
            is_registered: true,
            updated_at: new Date().toISOString()
          })
          .eq('telegram_id', telegramUser.telegram_id);
        
        if (updateError) {
          console.error(`  ❌ Error updating telegram user ${telegramUser.telegram_id}:`, updateError);
          errorCount++;
          continue;
        }
        
        console.log(`  ✅ Linked telegram user ${telegramUser.telegram_id} to user ID ${userId}`);
        fixedCount++;
        
      } catch (error) {
        console.error(`  ❌ Error processing telegram user ${telegramUser.telegram_id}:`, error);
        errorCount++;
      }
    }
    
    // Step 3: Verification
    console.log('\n📋 Step 3: Verifying fixes...');
    
    const { data: remainingBroken, error: verifyError } = await supabase
      .from('telegram_users')
      .select('count')
      .is('user_id', null);
    
    if (verifyError) {
      console.error('❌ Error verifying fixes:', verifyError);
    } else {
      const remainingCount = remainingBroken[0]?.count || 0;
      console.log(`🔍 Remaining broken records: ${remainingCount}`);
    }
    
    // Step 4: Summary
    console.log('\n📊 REPAIR SUMMARY:');
    console.log(`✅ Records fixed: ${fixedCount}`);
    console.log(`🆕 New users created: ${createdCount}`);
    console.log(`❌ Errors encountered: ${errorCount}`);
    console.log(`📋 Total processed: ${brokenTelegramUsers.length}`);
    
    if (fixedCount > 0) {
      console.log('\n🎉 DATA INTEGRITY RESTORED!');
      console.log('✅ All telegram_users records now have proper user_id links');
      console.log('✅ Foreign key relationships are intact');
      console.log('✅ Users can now access website features');
    }
    
    // Step 5: Test a few records
    console.log('\n📋 Step 5: Testing fixed records...');
    
    const { data: testRecords, error: testError } = await supabase
      .from('telegram_users')
      .select(`
        telegram_id,
        username,
        user_id,
        users!inner (
          id,
          username,
          email
        )
      `)
      .not('user_id', 'is', null)
      .limit(5);
    
    if (testError) {
      console.error('❌ Error testing records:', testError);
    } else {
      console.log('🔍 Sample of fixed records:');
      testRecords.forEach(record => {
        console.log(`  Telegram ${record.telegram_id} (@${record.username}) → User ${record.user_id} (${record.users.username})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Critical error during repair:', error);
  }
}

// Additional function to check system health
async function checkSystemHealth() {
  console.log('\n🏥 SYSTEM HEALTH CHECK');
  console.log('='.repeat(30));
  
  try {
    // Check telegram_users without user_id
    const { data: orphanedTelegram, error: orphanError } = await supabase
      .from('telegram_users')
      .select('count')
      .is('user_id', null);
    
    const orphanedCount = orphanedTelegram?.[0]?.count || 0;
    
    // Check users without telegram mapping
    const { data: allUsers, error: usersError } = await supabase
      .from('users')
      .select('count');
    
    const totalUsers = allUsers?.[0]?.count || 0;
    
    // Check telegram users with valid mapping
    const { data: linkedTelegram, error: linkedError } = await supabase
      .from('telegram_users')
      .select('count')
      .not('user_id', 'is', null);
    
    const linkedCount = linkedTelegram?.[0]?.count || 0;
    
    console.log('📊 SYSTEM STATISTICS:');
    console.log(`👥 Total users: ${totalUsers}`);
    console.log(`📱 Telegram users with valid links: ${linkedCount}`);
    console.log(`🚨 Orphaned telegram records: ${orphanedCount}`);
    
    if (orphanedCount === 0) {
      console.log('✅ SYSTEM HEALTHY: All telegram users properly linked');
    } else {
      console.log(`❌ SYSTEM UNHEALTHY: ${orphanedCount} telegram users missing user_id`);
    }
    
  } catch (error) {
    console.error('❌ Error checking system health:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--health-check')) {
    await checkSystemHealth();
  } else if (args.includes('--fix')) {
    await fixRegistrationDataIntegrity();
    await checkSystemHealth();
  } else {
    console.log('🚨 REGISTRATION DATA INTEGRITY REPAIR TOOL');
    console.log('='.repeat(50));
    console.log('Usage:');
    console.log('  node fix-registration-data-integrity.js --health-check  # Check system health');
    console.log('  node fix-registration-data-integrity.js --fix           # Fix broken records');
    console.log('');
    console.log('⚠️  WARNING: This script modifies user data');
    console.log('    Always backup your database before running --fix');
  }
}

main().catch(console.error);
