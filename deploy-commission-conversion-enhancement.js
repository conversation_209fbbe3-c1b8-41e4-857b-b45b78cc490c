#!/usr/bin/env node

/**
 * Deployment script for enhanced commission conversion function
 * This script deploys the enhanced process_commission_conversion SQL function
 * that includes referral commission generation for commission-to-shares conversions
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 Starting Commission Conversion Enhancement Deployment...');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function deployEnhancement() {
  try {
    console.log('📖 Reading SQL migration file...');
    
    // Read the SQL migration file
    const sqlFilePath = path.join(__dirname, 'migrations', '20250809_enhance_commission_conversion_function.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📝 SQL migration file loaded successfully');
    console.log(`📊 File size: ${sqlContent.length} characters`);
    
    console.log('🔧 Executing SQL migration...');
    
    // Execute the SQL migration
    const { data, error } = await supabase.rpc('exec', {
      sql: sqlContent
    });
    
    if (error) {
      console.error('❌ SQL execution failed:', error);
      throw error;
    }
    
    console.log('✅ SQL migration executed successfully');
    
    // Test the enhanced function
    console.log('🧪 Testing enhanced function availability...');
    
    // Try to call the function with test parameters (this should fail gracefully)
    const { error: testError } = await supabase.rpc('process_commission_conversion', {
      p_conversion_id: '00000000-0000-0000-0000-000000000000',
      p_admin_id: 0,
      p_admin_username: 'test'
    });
    
    // We expect this to fail with "Conversion not found" which means the function is working
    if (testError && testError.message.includes('Conversion not found')) {
      console.log('✅ Enhanced function is working correctly (test conversion not found as expected)');
    } else if (testError) {
      console.warn('⚠️ Function test returned unexpected error:', testError.message);
    } else {
      console.warn('⚠️ Function test returned unexpected success');
    }
    
    console.log('🎉 Commission Conversion Enhancement Deployment Complete!');
    console.log('');
    console.log('📋 Deployment Summary:');
    console.log('✅ Enhanced process_commission_conversion SQL function deployed');
    console.log('✅ Referral commission generation implemented');
    console.log('✅ Phase-dependent commission structure integrated');
    console.log('✅ Comprehensive audit logging added');
    console.log('✅ ACID transaction properties maintained');
    console.log('');
    console.log('🔍 Next Steps:');
    console.log('1. Monitor Railway logs for successful bot restart');
    console.log('2. Test commission conversions in live environment');
    console.log('3. Verify referral commissions are generated correctly');
    console.log('4. Check commission notifications are sent to referrers');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('1. Check Supabase connection and credentials');
    console.error('2. Verify SQL syntax in migration file');
    console.error('3. Ensure proper database permissions');
    console.error('4. Check Railway logs for additional error details');
    process.exit(1);
  }
}

// Execute deployment
deployEnhancement().catch(console.error);
