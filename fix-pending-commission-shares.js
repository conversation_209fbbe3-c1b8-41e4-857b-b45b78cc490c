#!/usr/bin/env node

/**
 * CRITICAL FIX: Commission Conversion Pending Shares Issue
 * 
 * This script fixes the issue where commission-to-shares conversions
 * show as "Pending Shares" instead of being included in active shares.
 * 
 * The fix: Update existing commission conversion purchases from 'pending' to 'active' status
 */

const { db } = require('./src/database/supabase-client');

async function fixPendingCommissionShares() {
  try {
    console.log('🔧 FIXING COMMISSION CONVERSION PENDING SHARES');
    console.log('==============================================');
    console.log('');
    
    // Check current state before fix
    console.log('📊 Checking current commission conversion purchases...');
    
    const { data: beforeFix, error: beforeError } = await db.client
      .from('aureus_share_purchases')
      .select('id, user_id, shares_purchased, payment_method, status, created_at')
      .eq('payment_method', 'Commission Conversion')
      .order('created_at', { ascending: false });
    
    if (beforeError) {
      console.error('❌ Error checking current state:', beforeError);
      return;
    }
    
    console.log(`📈 Found ${beforeFix?.length || 0} commission conversion purchases`);
    
    const pendingCount = beforeFix?.filter(p => p.status === 'pending').length || 0;
    const activeCount = beforeFix?.filter(p => p.status === 'active').length || 0;
    
    console.log(`   - Pending: ${pendingCount} (these will be fixed)`);
    console.log(`   - Active: ${activeCount} (already correct)`);
    console.log('');
    
    if (pendingCount === 0) {
      console.log('✅ No pending commission conversion shares found - nothing to fix!');
      return;
    }
    
    // Show details of pending shares
    const pendingShares = beforeFix?.filter(p => p.status === 'pending') || [];
    console.log('🔍 Pending commission conversion shares to fix:');
    pendingShares.forEach((share, index) => {
      console.log(`   ${index + 1}. User ${share.user_id}: ${share.shares_purchased} shares (${new Date(share.created_at).toLocaleDateString()})`);
    });
    console.log('');
    
    // Execute the fix
    console.log('🔧 Updating pending commission conversion shares to active status...');
    
    const { data: updateResult, error: updateError } = await db.client
      .from('aureus_share_purchases')
      .update({ 
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('payment_method', 'Commission Conversion')
      .eq('status', 'pending')
      .select('id, user_id, shares_purchased');
    
    if (updateError) {
      console.error('❌ Error updating shares:', updateError);
      return;
    }
    
    console.log(`✅ Successfully updated ${updateResult?.length || 0} commission conversion purchases to active status`);
    
    if (updateResult && updateResult.length > 0) {
      console.log('📋 Updated shares:');
      updateResult.forEach((share, index) => {
        console.log(`   ${index + 1}. User ${share.user_id}: ${share.shares_purchased} shares`);
      });
    }
    
    console.log('');
    
    // Verify the fix
    console.log('🔍 Verifying fix results...');
    
    const { data: afterFix, error: afterError } = await db.client
      .from('aureus_share_purchases')
      .select('id, user_id, shares_purchased, payment_method, status, created_at')
      .eq('payment_method', 'Commission Conversion')
      .order('created_at', { ascending: false });
    
    if (afterError) {
      console.error('❌ Error checking results:', afterError);
      return;
    }
    
    const pendingCountAfter = afterFix?.filter(p => p.status === 'pending').length || 0;
    const activeCountAfter = afterFix?.filter(p => p.status === 'active').length || 0;
    
    console.log(`📈 Commission conversion purchases after fix: ${afterFix?.length || 0}`);
    console.log(`   - Pending: ${pendingCountAfter} (should be 0)`);
    console.log(`   - Active: ${activeCountAfter} (should be all)`);
    
    if (pendingCountAfter === 0) {
      console.log('✅ SUCCESS: All commission conversion purchases are now active!');
    } else {
      console.log('⚠️  WARNING: Some purchases are still pending - manual review needed');
    }
    
    console.log('');
    console.log('🎉 Commission Conversion Fix Complete!');
    console.log('');
    console.log('📋 What was fixed:');
    console.log('✅ Commission conversion shares no longer show as "Pending"');
    console.log('✅ Users will see correct active share counts in their portfolio');
    console.log('✅ Commission-to-shares conversions are properly reflected');
    console.log('');
    console.log('🔍 Next Steps:');
    console.log('1. Users should restart their bot conversation to see updated portfolio');
    console.log('2. Monitor user reports to confirm issue is resolved');
    console.log('3. Test new commission conversions to verify they work correctly');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  }
}

// Run the fix
fixPendingCommissionShares();
