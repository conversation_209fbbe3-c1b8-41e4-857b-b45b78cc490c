-- FIX: Commission conversion shares showing as pending instead of active
-- This fixes the issue where commission-to-shares conversions create share purchase records
-- with 'pending' status instead of 'active' status, causing confusion for users

-- First, let's update any existing commission conversion share purchases that are incorrectly marked as pending
UPDATE aureus_share_purchases 
SET status = 'active'
WHERE payment_method = 'Commission Conversion' 
AND status = 'pending';

-- Now create/update the process_commission_conversion function to set correct status
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_conversion_id UUID,
    p_admin_id BIGINT,
    p_admin_username TEXT
) RETURNS VOID AS $$
DECLARE
    v_conversion RECORD;
    v_user RECORD;
    v_phase RECORD;
    v_referral RECORD;
    v_sponsor_usdt DECIMAL(10,2) := 0;
    v_sponsor_shares DECIMAL(10,2) := 0;
    v_purchase_id UUID;
BEGIN
    -- Get conversion details
    SELECT * INTO v_conversion
    FROM commission_conversions
    WHERE id = p_conversion_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Conversion not found or already processed';
    END IF;
    
    -- Get user details
    SELECT * INTO v_user
    FROM users
    WHERE id = v_conversion.user_id;
    
    -- Get phase details
    SELECT * INTO v_phase
    FROM investment_phases
    WHERE id = v_conversion.phase_id;
    
    -- Start transaction
    BEGIN
        -- Update conversion status to approved
        UPDATE commission_conversions
        SET 
            status = 'approved',
            approved_by_admin_id = p_admin_id,
            approved_at = NOW(),
            updated_at = NOW()
        WHERE id = p_conversion_id;
        
        -- Deduct USDT from commission balance
        UPDATE commission_balances
        SET 
            usdt_balance = usdt_balance - v_conversion.usdt_amount,
            escrowed_amount = GREATEST(0, escrowed_amount - v_conversion.usdt_amount),
            updated_at = NOW()
        WHERE user_id = v_conversion.user_id;
        
        -- Generate purchase ID
        v_purchase_id := gen_random_uuid();
        
        -- Create share purchase record with ACTIVE status (this is the key fix)
        INSERT INTO aureus_share_purchases (
            id,
            user_id,
            phase_id,
            package_name,
            shares_purchased,
            total_amount,
            price_per_share,
            commission_used,
            remaining_payment,
            payment_method,
            status,  -- This should be 'active', not 'pending'
            purchase_date,
            created_at,
            updated_at
        ) VALUES (
            v_purchase_id,
            v_conversion.user_id,
            v_conversion.phase_id,
            CONCAT(v_phase.phase_name, ' - Commission Conversion'),
            v_conversion.shares_requested,
            v_conversion.usdt_amount,
            v_conversion.share_price,
            v_conversion.usdt_amount,
            0,
            'Commission Conversion',
            'active',  -- FIXED: Set to 'active' instead of 'pending'
            NOW(),
            NOW(),
            NOW()
        );
        
        -- Update phase shares sold count
        UPDATE investment_phases
        SET 
            shares_sold = shares_sold + v_conversion.shares_requested,
            updated_at = NOW()
        WHERE id = v_conversion.phase_id;
        
        -- Check for referral and generate commission if applicable
        SELECT * INTO v_referral
        FROM referrals
        WHERE referred_id = v_conversion.user_id;
        
        IF FOUND THEN
            -- Calculate 15% commission for referrer
            v_sponsor_usdt := v_conversion.usdt_amount * 0.15;
            v_sponsor_shares := v_conversion.shares_requested * 0.15;
            
            -- Update referrer's commission balance
            INSERT INTO commission_balances (user_id, usdt_balance, share_balance, total_earned_usdt, total_earned_shares)
            VALUES (v_referral.referrer_id, v_sponsor_usdt, v_sponsor_shares, v_sponsor_usdt, v_sponsor_shares)
            ON CONFLICT (user_id) DO UPDATE SET
                usdt_balance = commission_balances.usdt_balance + v_sponsor_usdt,
                share_balance = commission_balances.share_balance + v_sponsor_shares,
                total_earned_usdt = commission_balances.total_earned_usdt + v_sponsor_usdt,
                total_earned_shares = commission_balances.total_earned_shares + v_sponsor_shares,
                updated_at = NOW();
            
            -- Create commission transaction record
            INSERT INTO commission_transactions (
                referrer_id,
                referred_id,
                share_purchase_id,
                usdt_commission,
                share_commission,
                commission_rate,
                share_purchase_amount,
                shares_purchased,
                status,
                payment_date,
                transaction_source
            ) VALUES (
                v_referral.referrer_id,
                v_conversion.user_id,
                v_purchase_id,
                v_sponsor_usdt,
                v_sponsor_shares,
                15.00,
                v_conversion.usdt_amount,
                v_conversion.shares_requested,
                'approved',
                NOW(),
                'commission_conversion'
            );
        END IF;
        
        -- Log admin action
        INSERT INTO admin_audit_logs (
            admin_id,
            admin_username,
            action,
            target_type,
            target_id,
            metadata,
            created_at
        ) VALUES (
            p_admin_id,
            p_admin_username,
            'commission_conversion_approved',
            'commission_conversion',
            p_conversion_id::TEXT,
            jsonb_build_object(
                'user_id', v_conversion.user_id,
                'shares_requested', v_conversion.shares_requested,
                'usdt_amount', v_conversion.usdt_amount,
                'phase_id', v_conversion.phase_id,
                'purchase_id', v_purchase_id,
                'sponsor_commission_usdt', v_sponsor_usdt,
                'sponsor_commission_shares', v_sponsor_shares
            ),
            NOW()
        );
        
    EXCEPTION WHEN OTHERS THEN
        -- Rollback will happen automatically
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to the service role
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, TEXT) TO service_role;

-- Verify the fix by checking any existing commission conversion purchases
SELECT 
    id,
    user_id,
    shares_purchased,
    payment_method,
    status,
    created_at
FROM aureus_share_purchases 
WHERE payment_method = 'Commission Conversion'
ORDER BY created_at DESC
LIMIT 10;
