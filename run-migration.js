const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting share transfer system migration...');

// Read environment variables from Railway (they should be set there)
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('This script should be run on Railway where environment variables are set.');
  console.log('The migration will be applied when the bot is deployed.');
  process.exit(0);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('📖 Reading migration file...');
    
    const migrationPath = path.join(__dirname, 'migrations', '20250809_create_share_transfer_system.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('🔧 Executing migration...');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.toLowerCase().includes('begin') || statement.toLowerCase().includes('commit')) {
        continue; // Skip transaction statements as Supabase handles them
      }
      
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err.message);
        // Continue with other statements
      }
    }
    
    console.log('🎉 Migration completed!');
    
    // Test the functions
    console.log('🧪 Testing migration...');
    
    // Test get_user_available_shares function
    const { data: testResult, error: testError } = await supabase
      .rpc('get_user_available_shares', { p_user_id: 1 });
    
    if (testError) {
      console.log('⚠️ Function test failed (this is expected if user 1 doesn\'t exist):', testError.message);
    } else {
      console.log('✅ Function test passed, result:', testResult);
    }
    
    console.log('✅ Share transfer system is ready!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

runMigration().then(() => {
  console.log('🏁 Migration script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Migration script failed:', error);
  process.exit(1);
});
