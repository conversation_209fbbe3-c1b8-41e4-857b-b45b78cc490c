# 📱 AUREUS MOBILE APP DEVELOPMENT SETUP GUIDE

## 🎯 OVERVIEW
This guide will help you set up your development environment to convert the Aureus Telegram bot into a React Native mobile application.

## 📋 PREREQUISITES INSTALLATION

### **Step 1: Install Node.js (if not already installed)**
```bash
# Check if Node.js is installed
node --version
npm --version

# If not installed, download from: https://nodejs.org/
# Choose LTS version (18.x or 20.x)
```

### **Step 2: Install React Native CLI**
```bash
npm install -g react-native-cli
npm install -g @react-native-community/cli
```

### **Step 3: Install Android Studio (for Android development)**
1. Download Android Studio from: https://developer.android.com/studio
2. Install with default settings
3. Open Android Studio and install:
   - Android SDK Platform 33 (Android 13)
   - Android SDK Build-Tools 33.0.0
   - Android Emulator
   - Android SDK Platform-Tools

### **Step 4: Set up Android Environment Variables**
Add to your system environment variables:
```bash
# Windows
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
Path += %ANDROID_HOME%\platform-tools
Path += %ANDROID_HOME%\tools
Path += %ANDROID_HOME%\tools\bin

# macOS/Linux
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### **Step 5: Install Java Development Kit (JDK)**
```bash
# Install JDK 11 or 17
# Windows: Download from Oracle or use Chocolatey
choco install openjdk11

# macOS: Use Homebrew
brew install openjdk@11

# Linux: Use package manager
sudo apt install openjdk-11-jdk
```

## 🏗️ PROJECT INITIALIZATION

### **Step 1: Create React Native Project**
```bash
# Navigate to your development directory
cd C:\Users\<USER>\Downloads\

# Create new React Native project
npx react-native init AureusApp --version 0.72.6

# Navigate to project directory
cd AureusApp
```

### **Step 2: Install Essential Dependencies**
```bash
# Navigation and UI libraries
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context
npm install react-native-gesture-handler react-native-reanimated

# Supabase integration
npm install @supabase/supabase-js
npm install react-native-url-polyfill

# UI Components and styling
npm install react-native-elements react-native-vector-icons
npm install react-native-paper

# Security and storage
npm install @react-native-async-storage/async-storage
npm install react-native-keychain
npm install react-native-biometrics

# Image and file handling
npm install react-native-image-picker
npm install react-native-document-picker

# Push notifications
npm install @react-native-firebase/app @react-native-firebase/messaging

# Crypto and payments
npm install react-native-crypto-js
npm install react-native-qrcode-scanner

# Forms and validation
npm install formik yup
npm install react-native-masked-text
```

### **Step 3: Configure Android Permissions**
Edit `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.USE_FINGERPRINT" />
<uses-permission android:name="android.permission.USE_BIOMETRIC" />
<uses-permission android:name="android.permission.VIBRATE" />
```

## 🧪 TESTING SETUP

### **Step 1: Start Metro Bundler**
```bash
# In project directory
npx react-native start
```

### **Step 2: Run on Android Emulator**
```bash
# In another terminal
npx react-native run-android
```

### **Step 3: Test on Physical Device**
1. Enable Developer Options on Android device
2. Enable USB Debugging
3. Connect device via USB
4. Run: `npx react-native run-android`

## 📁 PROJECT STRUCTURE SETUP

Create the following directory structure:
```
AureusApp/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # App screens
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API and Supabase services
│   ├── utils/              # Utility functions
│   ├── hooks/              # Custom React hooks
│   ├── context/            # React context providers
│   ├── assets/             # Images, fonts, etc.
│   └── constants/          # App constants
├── android/                # Android-specific code
├── ios/                    # iOS-specific code (future)
└── __tests__/              # Test files
```

## ✅ VERIFICATION CHECKLIST

- [ ] Node.js and npm installed
- [ ] React Native CLI installed
- [ ] Android Studio installed and configured
- [ ] Android SDK and build tools installed
- [ ] Environment variables set correctly
- [ ] JDK installed
- [ ] React Native project created
- [ ] Essential dependencies installed
- [ ] Android emulator running
- [ ] Test app runs successfully

## 🚨 TROUBLESHOOTING

### Common Issues:
1. **Metro bundler issues**: Clear cache with `npx react-native start --reset-cache`
2. **Android build failures**: Clean build with `cd android && ./gradlew clean`
3. **Dependency conflicts**: Delete node_modules and run `npm install`
4. **Emulator not starting**: Check Android Studio AVD Manager

## 📞 NEXT STEPS

Once setup is complete:
1. Test the default React Native app
2. Set up Supabase connection
3. Create basic navigation structure
4. Begin implementing authentication system

---

**Need Help?** Contact development team or refer to React Native documentation: https://reactnative.dev/docs/environment-setup
