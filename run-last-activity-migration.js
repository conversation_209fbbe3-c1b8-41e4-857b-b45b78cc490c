const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Running last_activity migration...');
    
    // Read the migration file
    const migrationSQL = fs.readFileSync('./migrations/20250110_add_last_activity_to_telegram_users.sql', 'utf8');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: migrationSQL 
    });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
    
    console.log('✅ Migration completed successfully!');
    
    // Test the new column
    console.log('🧪 Testing last_activity column...');
    
    const { data: testData, error: testError } = await supabase
      .from('telegram_users')
      .select('telegram_id, last_activity')
      .limit(1);
    
    if (testError) {
      console.error('⚠️ Test query failed:', testError);
    } else {
      console.log('✅ Test query successful:', testData);
    }
    
    console.log('🎉 last_activity column is ready!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
