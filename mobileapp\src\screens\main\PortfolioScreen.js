/**
 * Aureus Alliance Holdings Mobile App
 * Portfolio Screen - Investment Overview and History
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {Card, Button, Chip, Divider} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, typography, spacing, shadows} from '../../constants/theme';

const PortfolioScreen = ({navigation}) => {
  const {
    shares,
    totalShares,
    totalValue,
    isLoading,
    refreshPortfolio,
    getPortfolioSummary,
  } = usePortfolio();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const portfolioSummary = getPortfolioSummary();

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshPortfolio();
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'rejected':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'check-circle';
      case 'pending':
        return 'schedule';
      case 'rejected':
        return 'cancel';
      default:
        return 'help';
    }
  };

  const filterOptions = [
    {key: 'all', label: 'All', count: shares.length},
    {key: 'approved', label: 'Approved', count: shares.filter(s => s.status === 'approved').length},
    {key: 'pending', label: 'Pending', count: shares.filter(s => s.status === 'pending').length},
  ];

  const filteredShares = selectedFilter === 'all' 
    ? shares 
    : shares.filter(share => share.status === selectedFilter);

  const renderShareItem = ({item}) => (
    <Card style={styles.shareCard}>
      <Card.Content>
        <View style={styles.shareHeader}>
          <View style={styles.shareInfo}>
            <Text style={styles.shareAmount}>{formatCurrency(item.total_amount)}</Text>
            <Text style={styles.shareCount}>{item.shares_purchased} shares</Text>
          </View>
          <View style={styles.shareStatus}>
            <Icon 
              name={getStatusIcon(item.status)} 
              size={20} 
              color={getStatusColor(item.status)} 
            />
            <Text style={[styles.statusText, {color: getStatusColor(item.status)}]}>
              {item.status?.charAt(0).toUpperCase() + item.status?.slice(1)}
            </Text>
          </View>
        </View>
        
        <Divider style={styles.divider} />
        
        <View style={styles.shareDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Purchase Date</Text>
            <Text style={styles.detailValue}>{formatDate(item.created_at)}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment Method</Text>
            <Text style={styles.detailValue}>{item.payment_method || 'USDT'}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Share Price</Text>
            <Text style={styles.detailValue}>{formatCurrency(item.share_price || 5.00)}</Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      {/* Portfolio Summary Header */}
      <View style={styles.summarySection}>
        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.summaryHeader}>
              <Text style={styles.summaryTitle}>Total Portfolio Value</Text>
              <TouchableOpacity onPress={onRefresh}>
                <Icon name="refresh" size={24} color={colors.primary} />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.totalValue}>
              {formatCurrency(portfolioSummary.currentValue)}
            </Text>
            
            <View style={styles.summaryStats}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Total Shares</Text>
                <Text style={styles.statValue}>{totalShares.toFixed(2)}</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Invested</Text>
                <Text style={styles.statValue}>{formatCurrency(portfolioSummary.totalInvested)}</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>P&L</Text>
                <Text style={[
                  styles.statValue,
                  {color: portfolioSummary.profitLoss >= 0 ? colors.success : colors.error}
                ]}>
                  {portfolioSummary.profitLoss >= 0 ? '+' : ''}{formatCurrency(portfolioSummary.profitLoss)}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </View>

      {/* Filter Chips */}
      <View style={styles.filterSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterChips}>
            {filterOptions.map((option) => (
              <Chip
                key={option.key}
                selected={selectedFilter === option.key}
                onPress={() => setSelectedFilter(option.key)}
                style={[
                  styles.filterChip,
                  selectedFilter === option.key && styles.selectedChip
                ]}
                textStyle={[
                  styles.chipText,
                  selectedFilter === option.key && styles.selectedChipText
                ]}>
                {option.label} ({option.count})
              </Chip>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Shares List */}
      <View style={styles.sharesSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Investment History</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Purchase')}>
            <Text style={styles.addButton}>+ Buy More</Text>
          </TouchableOpacity>
        </View>

        {filteredShares.length > 0 ? (
          <FlatList
            data={filteredShares}
            renderItem={renderShareItem}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <View style={styles.emptyState}>
            <Icon name="account-balance-wallet" size={64} color={colors.textLight} />
            <Text style={styles.emptyTitle}>No Investments Yet</Text>
            <Text style={styles.emptySubtitle}>
              Start building your gold portfolio by purchasing your first shares
            </Text>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Purchase')}
              style={styles.emptyButton}
              contentStyle={styles.emptyButtonContent}>
              Buy Your First Shares
            </Button>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  summarySection: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  summaryCard: {
    ...shadows.md,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  summaryTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  totalValue: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.lg,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  statValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: colors.border,
    marginHorizontal: spacing.md,
  },
  filterSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  filterChips: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  filterChip: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  chipText: {
    color: colors.textSecondary,
  },
  selectedChipText: {
    color: colors.surface,
  },
  sharesSection: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  addButton: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    fontWeight: typography.fontWeight.semibold,
  },
  listContent: {
    paddingBottom: spacing.xl,
  },
  shareCard: {
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  shareHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  shareInfo: {
    flex: 1,
  },
  shareAmount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: 4,
  },
  shareCount: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
  },
  shareStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  statusText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  divider: {
    marginVertical: spacing.md,
  },
  shareDetails: {
    gap: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  detailValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
  },
  emptyButton: {
    backgroundColor: colors.primary,
  },
  emptyButtonContent: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
});

export default PortfolioScreen;
