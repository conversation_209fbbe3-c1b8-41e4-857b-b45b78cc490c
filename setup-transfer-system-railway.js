// Railway-compatible setup script for share transfer system
// This runs the migration automatically when the bot starts

const { createClient } = require('@supabase/supabase-js');

async function setupTransferSystemOnRailway() {
  console.log('🚀 [RAILWAY-SETUP] Setting up share transfer system...');
  
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.log('⚠️ [RAILWAY-SETUP] Missing Supabase environment variables - skipping setup');
    return false;
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Test if functions already exist
    console.log('🔍 [RAILWAY-SETUP] Testing if transfer functions exist...');
    
    const { data: testResult, error: testError } = await supabase
      .rpc('get_user_available_shares', { p_user_id: 1 });
    
    if (!testError) {
      console.log('✅ [RAILWAY-SETUP] Transfer functions already exist - system ready!');
      return true;
    }

    console.log('🔧 [RAILWAY-SETUP] Functions not found, creating them...');

    // Create the share transfer functions
    const migrationSQL = `
-- Create function to get user's total available shares for transfer
CREATE OR REPLACE FUNCTION get_user_available_shares(p_user_id BIGINT)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    v_total_shares DECIMAL(10,2) := 0;
BEGIN
    -- Sum all active shares from aureus_share_purchases
    SELECT COALESCE(SUM(shares_purchased), 0)
    INTO v_total_shares
    FROM aureus_share_purchases
    WHERE user_id = p_user_id 
    AND status = 'active';
    
    -- Add commission shares from commission_balances
    SELECT v_total_shares + COALESCE(share_balance, 0)
    INTO v_total_shares
    FROM commission_balances
    WHERE user_id = p_user_id;
    
    RETURN COALESCE(v_total_shares, 0);
END;
$$ LANGUAGE plpgsql;

-- Create function to validate share transfer
CREATE OR REPLACE FUNCTION validate_share_transfer(
    p_sender_user_id BIGINT,
    p_recipient_user_id BIGINT,
    p_shares_to_transfer DECIMAL(10,2)
) RETURNS JSONB AS $$
DECLARE
    v_errors TEXT[] := '{}';
    v_available_shares DECIMAL(10,2);
    v_sender_kyc TEXT;
    v_recipient_kyc TEXT;
    v_recipient_record RECORD;
BEGIN
    -- Validate recipient exists
    SELECT id, username, full_name INTO v_recipient_record
    FROM users WHERE id = p_recipient_user_id;
    
    IF NOT FOUND THEN
        v_errors := array_append(v_errors, 'Recipient user not found');
    END IF;
    
    -- Prevent self-transfer
    IF p_sender_user_id = p_recipient_user_id THEN
        v_errors := array_append(v_errors, 'Cannot transfer shares to yourself');
    END IF;
    
    -- Check sender KYC status
    SELECT kyc_status INTO v_sender_kyc
    FROM kyc_information 
    WHERE user_id = p_sender_user_id AND kyc_status = 'completed';
    
    IF NOT FOUND THEN
        v_errors := array_append(v_errors, 'Sender must complete KYC verification');
    END IF;
    
    -- Check recipient KYC status
    SELECT kyc_status INTO v_recipient_kyc
    FROM kyc_information 
    WHERE user_id = p_recipient_user_id AND kyc_status = 'completed';
    
    IF NOT FOUND THEN
        v_errors := array_append(v_errors, 'Recipient must complete KYC verification');
    END IF;
    
    -- Check sender has sufficient shares
    v_available_shares := get_user_available_shares(p_sender_user_id);
    
    IF v_available_shares < p_shares_to_transfer THEN
        v_errors := array_append(v_errors, 
            format('Insufficient shares. Available: %s, Requested: %s', 
                   v_available_shares, p_shares_to_transfer));
    END IF;
    
    -- Validate minimum transfer amount
    IF p_shares_to_transfer < 1 THEN
        v_errors := array_append(v_errors, 'Minimum transfer amount is 1 share');
    END IF;
    
    -- Return validation result
    RETURN jsonb_build_object(
        'valid', array_length(v_errors, 1) IS NULL,
        'errors', v_errors,
        'available_shares', v_available_shares
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to execute share transfer
CREATE OR REPLACE FUNCTION execute_share_transfer(
    p_sender_user_id BIGINT,
    p_recipient_user_id BIGINT,
    p_shares_to_transfer DECIMAL(10,2),
    p_transfer_reason TEXT DEFAULT NULL,
    p_initiated_by_telegram_id BIGINT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_validation_result JSONB;
    v_transfer_id UUID;
    v_current_phase RECORD;
    v_share_value DECIMAL(10,2);
BEGIN
    -- Validate the transfer first
    v_validation_result := validate_share_transfer(p_sender_user_id, p_recipient_user_id, p_shares_to_transfer);
    
    IF NOT (v_validation_result->>'valid')::BOOLEAN THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Transfer validation failed',
            'validation_errors', v_validation_result->'errors'
        );
    END IF;
    
    -- Get current phase for share value calculation
    SELECT * INTO v_current_phase
    FROM investment_phases
    WHERE is_active = true
    LIMIT 1;

    v_share_value := p_shares_to_transfer * COALESCE(v_current_phase.price_per_share, 5.00);
    
    -- Generate transfer ID
    v_transfer_id := gen_random_uuid();
    
    -- Create share purchase record for sender (negative) - NO COMMISSION GENERATION
    INSERT INTO aureus_share_purchases (
        user_id,
        package_id,
        package_name,
        shares_purchased,
        total_amount,
        commission_used,
        remaining_payment,
        payment_method,
        status,
        created_at,
        updated_at,
        transfer_reference_id
    ) VALUES (
        p_sender_user_id,
        NULL,
        'Share Transfer (Sent)',
        -p_shares_to_transfer,
        -v_share_value,
        0,
        -v_share_value,
        'TRANSFER_SENT_NO_COMMISSION',
        'active',
        NOW(),
        NOW(),
        v_transfer_id
    );

    -- Create share purchase record for recipient (positive) - NO COMMISSION GENERATION
    INSERT INTO aureus_share_purchases (
        user_id,
        package_id,
        package_name,
        shares_purchased,
        total_amount,
        commission_used,
        remaining_payment,
        payment_method,
        status,
        created_at,
        updated_at,
        transfer_reference_id
    ) VALUES (
        p_recipient_user_id,
        NULL,
        'Share Transfer (Received)',
        p_shares_to_transfer,
        v_share_value,
        0,
        v_share_value,
        'TRANSFER_RECEIVED_NO_COMMISSION',
        'active',
        NOW(),
        NOW(),
        v_transfer_id
    );
    
    -- Return success response
    RETURN jsonb_build_object(
        'success', true,
        'transfer_id', v_transfer_id,
        'shares_transferred', p_shares_to_transfer,
        'share_value', v_share_value,
        'completed_at', NOW()
    );

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ [TRANSFER] Error: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_available_shares(BIGINT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_available_shares(BIGINT) TO service_role;
GRANT EXECUTE ON FUNCTION validate_share_transfer(BIGINT, BIGINT, DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_share_transfer(BIGINT, BIGINT, DECIMAL) TO service_role;
GRANT EXECUTE ON FUNCTION execute_share_transfer(BIGINT, BIGINT, DECIMAL, TEXT, BIGINT) TO authenticated;
GRANT EXECUTE ON FUNCTION execute_share_transfer(BIGINT, BIGINT, DECIMAL, TEXT, BIGINT) TO service_role;
`;

    // Execute the migration
    const { error: migrationError } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (migrationError) {
      console.error('❌ [RAILWAY-SETUP] Migration failed:', migrationError);
      return false;
    }

    console.log('✅ [RAILWAY-SETUP] Share transfer functions created successfully!');
    
    // Test the functions
    const { data: finalTest, error: finalTestError } = await supabase
      .rpc('get_user_available_shares', { p_user_id: 1 });
    
    if (finalTestError) {
      console.log('⚠️ [RAILWAY-SETUP] Function test failed (expected if user 1 doesn\'t exist)');
    } else {
      console.log('✅ [RAILWAY-SETUP] Function test passed!');
    }

    console.log('🎉 [RAILWAY-SETUP] Share transfer system is ready!');
    return true;

  } catch (error) {
    console.error('💥 [RAILWAY-SETUP] Setup failed:', error);
    return false;
  }
}

module.exports = { setupTransferSystemOnRailway };
