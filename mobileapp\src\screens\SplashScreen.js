/**
 * Aureus Alliance Holdings Mobile App
 * Splash Screen - App Loading and Initialization
 */

import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Image,
  Animated,
} from 'react-native';
import {useAuth} from '../context/AuthContext';
import {colors, typography, spacing} from '../constants/theme';

const SplashScreen = ({navigation}) => {
  const {isAuthenticated, isLoading, checkAuthState} = useAuth();
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Check authentication state
    const initializeApp = async () => {
      try {
        // Minimum splash screen time for better UX
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Navigate based on authentication status
        if (isAuthenticated) {
          navigation.replace('MainTabs');
        } else {
          navigation.replace('Auth');
        }
      } catch (error) {
        console.error('Splash screen error:', error);
        navigation.replace('Auth');
      }
    };

    if (!isLoading) {
      initializeApp();
    }
  }, [isAuthenticated, isLoading, navigation, fadeAnim, scaleAnim]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
        ]}>
        {/* App Logo/Icon */}
        <View style={styles.logoContainer}>
          <View style={styles.logoPlaceholder}>
            <Text style={styles.logoText}>AU</Text>
          </View>
        </View>

        {/* App Name */}
        <Text style={styles.appName}>Aureus Alliance</Text>
        <Text style={styles.appSubtitle}>Gold Investment Platform</Text>

        {/* Loading Indicator */}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.secondary} />
          <Text style={styles.loadingText}>Loading your portfolio...</Text>
        </View>

        {/* Version Info */}
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </Animated.View>

      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        <View style={[styles.circle, styles.circle1]} />
        <View style={[styles.circle, styles.circle2]} />
        <View style={[styles.circle, styles.circle3]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    zIndex: 1,
  },
  logoContainer: {
    marginBottom: spacing.xl,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.primary,
  },
  appName: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.secondary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  appSubtitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    color: colors.surface,
    marginBottom: spacing['2xl'],
    textAlign: 'center',
    opacity: 0.9,
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: spacing['2xl'],
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.surface,
    marginTop: spacing.md,
    opacity: 0.8,
  },
  versionText: {
    fontSize: typography.fontSize.sm,
    color: colors.surface,
    opacity: 0.6,
    position: 'absolute',
    bottom: -100,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  circle: {
    position: 'absolute',
    borderRadius: 9999,
    backgroundColor: colors.secondary,
    opacity: 0.1,
  },
  circle1: {
    width: 200,
    height: 200,
    top: -100,
    right: -100,
  },
  circle2: {
    width: 150,
    height: 150,
    bottom: -75,
    left: -75,
  },
  circle3: {
    width: 100,
    height: 100,
    top: '30%',
    left: -50,
  },
});

export default SplashScreen;
