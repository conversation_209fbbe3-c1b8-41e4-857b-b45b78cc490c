/**
 * Aureus Alliance Holdings Mobile App
 * User Migration Service
 * 
 * Handles migration of existing Telegram users to mobile app authentication
 * and creation of new mobile-only users
 */

import {supabase, authService} from './supabaseClient';

export const userMigrationService = {
  /**
   * Link existing Telegram user to mobile app authentication
   * This allows Telegram users to access the mobile app with their existing data
   */
  async linkTelegramUserToMobileApp(email, password, telegramId) {
    try {
      console.log(`🔄 Linking Telegram user ${telegramId} to mobile app...`);

      // First, check if this Telegram user exists
      const {data: telegramUser, error: telegramError} = await supabase
        .from('telegram_users')
        .select(`
          telegram_id,
          user_id,
          username,
          first_name,
          last_name,
          users!inner (
            id,
            username,
            email,
            full_name,
            auth_user_id
          )
        `)
        .eq('telegram_id', telegramId)
        .single();

      if (telegramError) {
        throw new Error(`Telegram user not found: ${telegramError.message}`);
      }

      const user = telegramUser.users;

      // Check if user already has auth_user_id (already linked)
      if (user.auth_user_id) {
        return {
          success: false,
          error: 'This Telegram account is already linked to mobile app access'
        };
      }

      // Create Supabase auth user
      const {data: authUser, error: authError} = await supabase.auth.admin.createUser({
        email: email,
        password: password,
        email_confirm: true,
        user_metadata: {
          telegram_id: telegramId,
          source: 'telegram_migration',
          username: user.username,
          full_name: user.full_name || `${telegramUser.first_name} ${telegramUser.last_name}`.trim()
        }
      });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      // Update users table with auth_user_id and mobile app fields
      const {error: updateError} = await supabase
        .from('users')
        .update({
          auth_user_id: authUser.user.id,
          email: email,
          mobile_app_enabled: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (updateError) {
        // Cleanup: delete the auth user we just created
        await supabase.auth.admin.deleteUser(authUser.user.id);
        throw new Error(`Failed to link accounts: ${updateError.message}`);
      }

      console.log(`✅ Successfully linked Telegram user ${telegramId} to mobile app`);

      return {
        success: true,
        data: {
          authUserId: authUser.user.id,
          userId: user.id,
          username: user.username,
          email: email
        }
      };

    } catch (error) {
      console.error('Link Telegram user error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Create new mobile-only user (no Telegram account)
   */
  async createMobileOnlyUser(email, password, userData) {
    try {
      console.log(`🔄 Creating new mobile-only user: ${email}`);

      // Check if email already exists
      const {data: existingUser} = await supabase
        .from('users')
        .select('id, email')
        .eq('email', email)
        .single();

      if (existingUser) {
        return {
          success: false,
          error: 'An account with this email already exists'
        };
      }

      // Create Supabase auth user
      const {data: authUser, error: authError} = await supabase.auth.admin.createUser({
        email: email,
        password: password,
        email_confirm: true,
        user_metadata: {
          source: 'mobile_app',
          full_name: userData.fullName,
          phone: userData.phone,
          country: userData.country
        }
      });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      // Create user record in users table
      const {data: user, error: userError} = await supabase
        .from('users')
        .insert({
          username: userData.fullName?.toLowerCase().replace(/\s+/g, '_') || email.split('@')[0],
          email: email,
          full_name: userData.fullName,
          phone: userData.phone,
          country: userData.country,
          auth_user_id: authUser.user.id,
          mobile_app_enabled: true,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (userError) {
        // Cleanup: delete the auth user we just created
        await supabase.auth.admin.deleteUser(authUser.user.id);
        throw new Error(`Failed to create user record: ${userError.message}`);
      }

      // Initialize commission balance for new user
      const {error: commissionError} = await supabase
        .from('commission_balances')
        .insert({
          user_id: user.id,
          usdt_balance: 0.00,
          share_balance: 0.00,
          total_usdt_earned: 0.00,
          total_shares_earned: 0.00,
          created_at: new Date().toISOString()
        });

      if (commissionError) {
        console.warn('Failed to create commission balance:', commissionError);
        // Don't fail the entire registration for this
      }

      console.log(`✅ Successfully created mobile-only user: ${email}`);

      return {
        success: true,
        data: {
          authUserId: authUser.user.id,
          userId: user.id,
          username: user.username,
          email: email,
          fullName: user.full_name
        }
      };

    } catch (error) {
      console.error('Create mobile-only user error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Check if a user can access mobile app features
   */
  async checkMobileAppAccess(authUserId) {
    try {
      const {data: user, error} = await supabase
        .from('users')
        .select('id, username, mobile_app_enabled, is_active')
        .eq('auth_user_id', authUserId)
        .single();

      if (error) {
        return {success: false, error: 'User not found'};
      }

      if (!user.is_active) {
        return {success: false, error: 'Account is deactivated'};
      }

      return {
        success: true,
        data: {
          hasAccess: user.mobile_app_enabled || false,
          userId: user.id,
          username: user.username
        }
      };

    } catch (error) {
      console.error('Check mobile app access error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Enable mobile app access for existing user
   */
  async enableMobileAppAccess(userId) {
    try {
      const {error} = await supabase
        .from('users')
        .update({
          mobile_app_enabled: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      return {success: true};
    } catch (error) {
      console.error('Enable mobile app access error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Update FCM token for push notifications
   */
  async updateFCMToken(authUserId, fcmToken) {
    try {
      const {error} = await supabase
        .from('users')
        .update({
          fcm_token: fcmToken,
          updated_at: new Date().toISOString()
        })
        .eq('auth_user_id', authUserId);

      if (error) throw error;

      return {success: true};
    } catch (error) {
      console.error('Update FCM token error:', error);
      return {success: false, error: error.message};
    }
  },

  /**
   * Get user's referral information for mobile app
   */
  async getUserReferralInfo(userId) {
    try {
      // Get referral data
      const {data: referrals, error: referralError} = await supabase
        .from('referrals')
        .select(`
          id,
          referrer_user_id,
          referred_user_id,
          created_at,
          users!referrals_referred_user_id_fkey (
            username,
            full_name
          )
        `)
        .eq('referrer_user_id', userId);

      if (referralError) throw referralError;

      // Get commission summary
      const {data: commissions, error: commissionError} = await supabase
        .from('commission_balances')
        .select('usdt_balance, share_balance, total_usdt_earned, total_shares_earned')
        .eq('user_id', userId)
        .single();

      if (commissionError) throw commissionError;

      return {
        success: true,
        data: {
          referrals: referrals || [],
          commissions: commissions || {
            usdt_balance: 0,
            share_balance: 0,
            total_usdt_earned: 0,
            total_shares_earned: 0
          }
        }
      };

    } catch (error) {
      console.error('Get user referral info error:', error);
      return {success: false, error: error.message};
    }
  }
};

export default userMigrationService;
