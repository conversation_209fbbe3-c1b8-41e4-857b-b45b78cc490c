# 🚀 Complete Setup Guide - Aureus Mobile App

This guide will walk you through setting up the Aureus Alliance Holdings mobile app from scratch.

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- [ ] Windows 10/11, macOS, or Linux
- [ ] Node.js 16+ installed
- [ ] Git installed
- [ ] Android Studio installed
- [ ] Java Development Kit (JDK) 11 or 17
- [ ] Active Supabase project
- [ ] Google Play Console account ($25 one-time fee)

## 🔧 Step 1: Development Environment Setup

### Install Node.js
```bash
# Check if Node.js is installed
node --version
npm --version

# If not installed, download from: https://nodejs.org/
# Choose LTS version (18.x or 20.x)
```

### Install React Native CLI
```bash
npm install -g react-native-cli
npm install -g @react-native-community/cli
```

### Install Android Studio
1. Download from: https://developer.android.com/studio
2. Install with default settings
3. Open Android Studio and install:
   - Android SDK Platform 33 (Android 13)
   - Android SDK Build-Tools 33.0.0
   - Android Emulator
   - Android SDK Platform-Tools

### Set Environment Variables (Windows)
```bash
# Add to System Environment Variables:
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
Path += %ANDROID_HOME%\platform-tools
Path += %ANDROID_HOME%\tools
Path += %ANDROID_HOME%\tools\bin
```

### Set Environment Variables (macOS/Linux)
```bash
# Add to ~/.bashrc or ~/.zshrc:
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

## 📱 Step 2: Project Setup

### Navigate to Mobile App Directory
```bash
cd mobileapp
```

### Install Dependencies
```bash
npm install
```

### Configure Supabase Connection
1. Open `src/services/supabaseClient.js`
2. Replace placeholder values:
```javascript
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'your_actual_supabase_anon_key_here';
```

## 🔧 Step 3: Android Configuration

### Update Package Name
1. Open `android/app/src/main/java/com/aureusapp/MainActivity.java`
2. Update package name to match your desired app ID

### Configure App Icon
1. Replace icons in `android/app/src/main/res/mipmap-*/`
2. Use Android Asset Studio for proper sizing

### Update App Name
1. Edit `android/app/src/main/res/values/strings.xml`:
```xml
<resources>
    <string name="app_name">Aureus Alliance</string>
</resources>
```

## 🧪 Step 4: Testing Setup

### Start Android Emulator
1. Open Android Studio
2. Go to AVD Manager
3. Create/start an Android Virtual Device

### Start Metro Bundler
```bash
npm start
```

### Run on Android
```bash
# In another terminal
npm run android
```

## 🔐 Step 5: Security Configuration

### Generate Keystore for Release
```bash
cd android/app
keytool -genkeypair -v -storetype PKCS12 -keystore aureus-release-key.keystore -alias aureus-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### Configure Gradle for Signing
1. Create `android/gradle.properties`:
```properties
AUREUS_UPLOAD_STORE_FILE=aureus-release-key.keystore
AUREUS_UPLOAD_KEY_ALIAS=aureus-key-alias
AUREUS_UPLOAD_STORE_PASSWORD=your_keystore_password
AUREUS_UPLOAD_KEY_PASSWORD=your_key_password
```

2. Update `android/app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            if (project.hasProperty('AUREUS_UPLOAD_STORE_FILE')) {
                storeFile file(AUREUS_UPLOAD_STORE_FILE)
                storePassword AUREUS_UPLOAD_STORE_PASSWORD
                keyAlias AUREUS_UPLOAD_KEY_ALIAS
                keyPassword AUREUS_UPLOAD_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## 📦 Step 6: Build for Production

### Build Release APK
```bash
cd android
./gradlew assembleRelease
```

### Build Release AAB (for Play Store)
```bash
cd android
./gradlew bundleRelease
```

## 🏪 Step 7: Google Play Store Setup

### Create Play Console Account
1. Visit: https://play.google.com/console
2. Pay $25 one-time registration fee
3. Complete developer profile

### Create App Listing
1. Click "Create app"
2. Fill in app details:
   - **App name**: Aureus Alliance Holdings
   - **Default language**: English (United States)
   - **App or game**: App
   - **Free or paid**: Free

### Prepare Store Assets
Create the following assets:

#### App Icon
- 512 x 512 PNG
- High-resolution app icon

#### Screenshots
- Phone screenshots (minimum 2, maximum 8)
- 7-inch tablet screenshots (optional)
- 10-inch tablet screenshots (optional)

#### Feature Graphic
- 1024 x 500 PNG
- Promotional banner for Play Store

### Upload App Bundle
1. Go to "Release" > "Production"
2. Click "Create new release"
3. Upload your AAB file
4. Add release notes
5. Review and rollout

## 🔧 Step 8: App Configuration

### Configure Deep Links
Update `android/app/src/main/AndroidManifest.xml`:
```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="https"
          android:host="aureus-alliance.com" />
</intent-filter>
```

### Setup Push Notifications (Optional)
1. Create Firebase project
2. Add Android app to Firebase
3. Download `google-services.json`
4. Place in `android/app/`

## 🚀 Step 9: Deployment Checklist

### Pre-Launch Testing
- [ ] Test on multiple Android devices/emulators
- [ ] Test all user flows (registration, login, purchase, etc.)
- [ ] Verify Supabase integration works
- [ ] Test payment flows
- [ ] Verify push notifications (if implemented)

### Store Listing Optimization
- [ ] Compelling app description
- [ ] Relevant keywords for ASO
- [ ] High-quality screenshots
- [ ] Proper app categorization
- [ ] Privacy policy URL
- [ ] Terms of service URL

### Launch Preparation
- [ ] Set up app analytics
- [ ] Prepare customer support channels
- [ ] Create user onboarding materials
- [ ] Plan marketing/announcement strategy

## 📊 Step 10: Post-Launch Monitoring

### Key Metrics to Track
- App downloads and installs
- User registration conversion
- Crash-free sessions (target: 99.9%+)
- App store rating (target: 4.5+)
- User retention rates

### Monitoring Tools
- Google Play Console (crashes, ANRs, reviews)
- Firebase Analytics (user behavior)
- Supabase Dashboard (database performance)

## 🐛 Troubleshooting Common Issues

### Build Failures
```bash
# Clean and rebuild
cd android
./gradlew clean
cd ..
npm start -- --reset-cache
```

### Metro Bundler Issues
```bash
# Reset Metro cache
npx react-native start --reset-cache

# Clear node modules
rm -rf node_modules
npm install
```

### Android Emulator Issues
1. Check AVD Manager in Android Studio
2. Ensure emulator has enough RAM (4GB+)
3. Enable hardware acceleration

### Supabase Connection Issues
1. Verify URL and keys are correct
2. Check network connectivity
3. Ensure RLS policies allow access

## 📞 Support Resources

### Documentation
- [React Native Docs](https://reactnative.dev/docs/getting-started)
- [Supabase React Native Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-react-native)
- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)

### Community Support
- React Native Community Discord
- Stack Overflow (tag: react-native)
- Supabase Discord Community

---

## 🎉 Congratulations!

You've successfully set up the Aureus Alliance Holdings mobile app! Your users can now access their gold investment portfolio directly from their mobile devices, free from Telegram's premium requirements.

**Next Steps:**
1. Test thoroughly on various devices
2. Submit to Google Play Store
3. Monitor user feedback and app performance
4. Plan iOS version development (if needed)

**Remember:** This mobile app provides the same functionality as your Telegram bot but with an enhanced mobile-native experience that your users will love!
