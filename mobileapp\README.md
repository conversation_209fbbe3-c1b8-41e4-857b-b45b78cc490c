# 📱 Aureus Alliance Holdings Mobile App

A React Native mobile application for gold investment and portfolio management, converted from the existing Telegram bot system.

## 🎯 Overview

This mobile app provides all the functionality of the Aureus Alliance Holdings Telegram bot in a native mobile experience:

- **User Authentication**: Email/password registration and login
- **Portfolio Management**: View and track gold share investments
- **Share Purchases**: Buy gold shares with USDT or bank transfers
- **Commission System**: Dual commission structure (15% USDT + 15% shares)
- **Referral Program**: Share referral links and earn commissions
- **Real-time Updates**: Live portfolio and commission tracking

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development - macOS only)

### Installation

1. **Clone and navigate to the mobile app directory:**
   ```bash
   cd mobileapp
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure Supabase:**
   - Update `src/services/supabaseClient.js` with your Supabase URL and keys
   - Ensure your Supabase project has the required tables and RLS policies

4. **Android Setup:**
   ```bash
   # Install Android dependencies
   cd android
   ./gradlew clean
   cd ..
   
   # Start Metro bundler
   npm start
   
   # Run on Android (in another terminal)
   npm run android
   ```

5. **iOS Setup (macOS only):**
   ```bash
   # Install iOS dependencies
   cd ios
   pod install
   cd ..
   
   # Run on iOS
   npm run ios
   ```

## 📁 Project Structure

```
mobileapp/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # App screens
│   │   ├── auth/           # Authentication screens
│   │   └── main/           # Main app screens
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API and Supabase services
│   ├── context/            # React context providers
│   ├── constants/          # App constants and theme
│   └── utils/              # Utility functions
├── android/                # Android-specific code
├── ios/                    # iOS-specific code (future)
└── __tests__/              # Test files
```

## 🎨 Features

### Authentication System
- Email/password registration and login
- Password reset functionality
- Secure session management
- Biometric authentication support

### Portfolio Management
- Real-time portfolio value tracking
- Investment history with status indicators
- Share balance and performance metrics
- Profit/loss calculations

### Share Purchase System
- Custom amount input ($5 - $50,000)
- Multiple payment methods (USDT, Bank Transfer)
- Payment proof upload
- Admin approval workflow

### Commission & Referral System
- Dual commission structure display
- USDT withdrawal requests
- Referral link sharing
- Commission history tracking

### User Profile & Settings
- Profile management
- Security settings
- Notification preferences
- Support and help resources

## 🔧 Configuration

### Supabase Setup

Update `src/services/supabaseClient.js`:

```javascript
const SUPABASE_URL = 'your_supabase_url_here';
const SUPABASE_ANON_KEY = 'your_supabase_anon_key_here';
```

### Theme Customization

Modify `src/constants/theme.js` to customize:
- Colors and branding
- Typography settings
- Spacing and layout
- Component styles

## 📱 Build for Production

### Android APK/AAB

```bash
# Generate signed APK
cd android
./gradlew assembleRelease

# Generate signed AAB (for Play Store)
./gradlew bundleRelease
```

### iOS (macOS only)

```bash
# Build for iOS
cd ios
xcodebuild -workspace AureusApp.xcworkspace -scheme AureusApp -configuration Release
```

## 🚀 Deployment

### Google Play Store

1. **Prepare assets:**
   - App icon (512x512 PNG)
   - Screenshots (various device sizes)
   - App description and metadata

2. **Upload to Play Console:**
   - Create app listing
   - Upload AAB file
   - Configure store listing
   - Submit for review

### App Store (iOS)

1. **Prepare for App Store:**
   - Configure app in App Store Connect
   - Upload build via Xcode or Transporter
   - Complete app metadata
   - Submit for review

## 🔐 Security Features

- JWT token secure storage
- Biometric authentication
- Encrypted local data storage
- Certificate pinning for API calls
- Session timeout handling

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run on Android emulator
npm run android

# Run on iOS simulator (macOS only)
npm run ios
```

## 📊 Performance Optimization

- Image compression and lazy loading
- Code splitting and bundle optimization
- Database query optimization
- Caching strategies for frequently accessed data
- Background task management

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler issues:**
   ```bash
   npm start -- --reset-cache
   ```

2. **Android build failures:**
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

3. **Dependency conflicts:**
   ```bash
   rm -rf node_modules && npm install
   ```

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [React Native Docs](https://reactnative.dev/docs/getting-started)
- Supabase Docs: [Supabase React Native Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-react-native)

## 📄 License

This project is proprietary software owned by Aureus Alliance Holdings.

---

**Built with ❤️ using React Native and Supabase**
