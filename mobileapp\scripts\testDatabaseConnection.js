/**
 * PRODUCTION DATABASE CONNECTION TEST SCRIPT
 *
 * ⚠️  CRITICAL: This connects to a LIVE PRODUCTION DATABASE with real customer money
 * 🔒 READ-ONLY OPERATIONS ONLY - No INSERT/UPDATE/DELETE operations
 * 💰 Contains real user investments and financial transactions
 *
 * This script verifies mobile app connectivity to the production Supabase database
 */

const { createClient } = require('@supabase/supabase-js');

// Use the same credentials as your mobile app
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseConnection() {
  console.log('🔄 Testing PRODUCTION Supabase database connection...');
  console.log('⚠️  READ-ONLY operations only - Live customer data\n');

  try {
    // Test 1: Basic connection
    console.log('1️⃣ Testing basic connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Connection failed:', connectionError.message);
      return false;
    }
    console.log('✅ Basic connection successful\n');

    // Test 2: Check required tables exist
    console.log('2️⃣ Checking required tables...');
    const requiredTables = [
      'users',
      'telegram_users', 
      'aureus_share_purchases',
      'commission_balances',
      'investment_phases'
    ];

    for (const table of requiredTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`❌ Table '${table}' not accessible:`, error.message);
          return false;
        }
        console.log(`✅ Table '${table}' accessible`);
      } catch (err) {
        console.error(`❌ Table '${table}' error:`, err.message);
        return false;
      }
    }
    console.log('');

    // Test 3: Check production data (READ-ONLY)
    console.log('3️⃣ Checking production data (READ-ONLY)...');

    // Get total user count without exposing individual user data
    const { count: userCount, error: usersError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    if (usersError) {
      console.error('❌ Cannot read users table:', usersError.message);
      return false;
    }

    console.log(`✅ Found ${userCount} total users in production database`);
    console.log('   (Individual user data protected - production privacy)');
    console.log('');

    // Test 4: Check share purchases (aggregated data only)
    const { count: purchaseCount, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*', { count: 'exact', head: true });

    if (sharesError) {
      console.error('❌ Cannot read share purchases:', sharesError.message);
      return false;
    }

    // Get total investment value (aggregated, no individual data)
    const { data: totalStats, error: statsError } = await supabase
      .from('aureus_share_purchases')
      .select('total_amount.sum(), shares_purchased.sum()')
      .single();

    console.log(`✅ Found ${purchaseCount} total share purchases in production`);
    if (!statsError && totalStats) {
      console.log(`   Total investment value: $${totalStats.sum || 0}`);
      console.log(`   Total shares purchased: ${totalStats.sum_1 || 0}`);
    }
    console.log('   (Individual purchase data protected - production privacy)');
    console.log('');

    // Test 5: Check commission balances (aggregated data only)
    const { count: commissionCount, error: commissionsError } = await supabase
      .from('commission_balances')
      .select('*', { count: 'exact', head: true });

    if (commissionsError) {
      console.error('❌ Cannot read commission balances:', commissionsError.message);
      return false;
    }

    console.log(`✅ Found ${commissionCount} commission balance records`);
    console.log('   (Individual commission data protected - production privacy)');
    console.log('');

    // Test 6: Check current investment phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError) {
      console.warn('⚠️ No active investment phase found:', phaseError.message);

      // Try to get any investment phase to see the structure
      const { data: anyPhase, error: anyPhaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .limit(1)
        .single();

      if (!anyPhaseError && anyPhase) {
        console.log('📊 Investment phase table structure:');
        console.log('   Available columns:', Object.keys(anyPhase));
        console.log('   Sample data:', anyPhase);
      }
    } else {
      console.log(`✅ Current investment phase: ${currentPhase.phase_name || 'Unknown'}`);
      console.log(`   Share price: $${currentPhase.price_per_share}`);
      console.log(`   Shares sold: ${currentPhase.shares_sold || 0} / ${currentPhase.total_shares_available || 0}`);
      const remaining = (currentPhase.total_shares_available || 0) - (currentPhase.shares_sold || 0);
      console.log(`   Shares remaining: ${remaining}`);
    }
    console.log('');

    console.log('🎉 PRODUCTION DATABASE CONNECTION TEST SUCCESSFUL!');
    console.log('');
    console.log('✅ Mobile app can safely access production database');
    console.log('✅ All required tables are accessible with proper permissions');
    console.log('✅ Production user data is protected and accessible');
    console.log('✅ Financial data (shares/commissions) is accessible');
    console.log('✅ Investment phase configuration is active');
    console.log('');
    console.log('🚀 Ready for production mobile app deployment!');
    console.log('⚠️  Remember: This connects to LIVE customer data and real money');

    return true;

  } catch (error) {
    console.error('❌ Unexpected error during database test:', error);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testDatabaseConnection()
    .then(success => {
      if (!success) {
        console.log('\n❌ Database connection test failed!');
        console.log('');
        console.log('🔧 Troubleshooting steps:');
        console.log('1. Verify SUPABASE_URL and SUPABASE_ANON_KEY are correct');
        console.log('2. Check that your Supabase project is active');
        console.log('3. Ensure RLS policies allow read access');
        console.log('4. Confirm your network connection');
        console.log('');
        console.log('📖 See DATABASE_SETUP.md for detailed instructions');
        process.exit(1);
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test script error:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
