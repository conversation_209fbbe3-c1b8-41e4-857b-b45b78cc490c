/**
 * Database Connection Test Script
 * 
 * This script verifies that the mobile app can connect to the same
 * Supabase database as your Telegram bot
 */

const { createClient } = require('@supabase/supabase-js');

// Use the same credentials as your mobile app
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseConnection() {
  console.log('🔄 Testing Supabase database connection...\n');

  try {
    // Test 1: Basic connection
    console.log('1️⃣ Testing basic connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Connection failed:', connectionError.message);
      return false;
    }
    console.log('✅ Basic connection successful\n');

    // Test 2: Check required tables exist
    console.log('2️⃣ Checking required tables...');
    const requiredTables = [
      'users',
      'telegram_users', 
      'aureus_share_purchases',
      'commission_balances',
      'investment_phases'
    ];

    for (const table of requiredTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`❌ Table '${table}' not accessible:`, error.message);
          return false;
        }
        console.log(`✅ Table '${table}' accessible`);
      } catch (err) {
        console.error(`❌ Table '${table}' error:`, err.message);
        return false;
      }
    }
    console.log('');

    // Test 3: Check data exists
    console.log('3️⃣ Checking existing data...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email')
      .limit(5);

    if (usersError) {
      console.error('❌ Cannot read users:', usersError.message);
      return false;
    }

    console.log(`✅ Found ${users.length} users in database`);
    if (users.length > 0) {
      console.log('   Sample users:');
      users.forEach(user => {
        console.log(`   - ${user.username} (${user.email || 'no email'})`);
      });
    }
    console.log('');

    // Test 4: Check share purchases
    const { data: shares, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('id, user_id, shares_purchased, total_amount, status')
      .limit(3);

    if (sharesError) {
      console.error('❌ Cannot read share purchases:', sharesError.message);
      return false;
    }

    console.log(`✅ Found ${shares.length} share purchases`);
    if (shares.length > 0) {
      console.log('   Sample purchases:');
      shares.forEach(share => {
        console.log(`   - User ${share.user_id}: ${share.shares_purchased} shares ($${share.total_amount}) - ${share.status}`);
      });
    }
    console.log('');

    // Test 5: Check commission balances
    const { data: commissions, error: commissionsError } = await supabase
      .from('commission_balances')
      .select('user_id, usdt_balance, share_balance')
      .limit(3);

    if (commissionsError) {
      console.error('❌ Cannot read commission balances:', commissionsError.message);
      return false;
    }

    console.log(`✅ Found ${commissions.length} commission records`);
    if (commissions.length > 0) {
      console.log('   Sample commissions:');
      commissions.forEach(comm => {
        console.log(`   - User ${comm.user_id}: $${comm.usdt_balance} USDT, ${comm.share_balance} shares`);
      });
    }
    console.log('');

    // Test 6: Check current investment phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError) {
      console.warn('⚠️ No active investment phase found:', phaseError.message);

      // Try to get any investment phase to see the structure
      const { data: anyPhase, error: anyPhaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .limit(1)
        .single();

      if (!anyPhaseError && anyPhase) {
        console.log('📊 Investment phase table structure:');
        console.log('   Available columns:', Object.keys(anyPhase));
        console.log('   Sample data:', anyPhase);
      }
    } else {
      console.log(`✅ Current investment phase: ${currentPhase.phase_name || currentPhase.name || 'Unknown'}`);
      if (currentPhase.share_price) {
        console.log(`   Share price: $${currentPhase.share_price}`);
      } else if (currentPhase.price) {
        console.log(`   Share price: $${currentPhase.price}`);
      } else {
        console.log('   Share price: Not specified');
      }
    }
    console.log('');

    console.log('🎉 DATABASE CONNECTION TEST SUCCESSFUL!');
    console.log('');
    console.log('✅ Your mobile app can access the same database as your Telegram bot');
    console.log('✅ All required tables are accessible');
    console.log('✅ Existing user data is available');
    console.log('✅ Portfolio and commission data is accessible');
    console.log('');
    console.log('🚀 Ready to proceed with mobile app development!');

    return true;

  } catch (error) {
    console.error('❌ Unexpected error during database test:', error);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testDatabaseConnection()
    .then(success => {
      if (!success) {
        console.log('\n❌ Database connection test failed!');
        console.log('');
        console.log('🔧 Troubleshooting steps:');
        console.log('1. Verify SUPABASE_URL and SUPABASE_ANON_KEY are correct');
        console.log('2. Check that your Supabase project is active');
        console.log('3. Ensure RLS policies allow read access');
        console.log('4. Confirm your network connection');
        console.log('');
        console.log('📖 See DATABASE_SETUP.md for detailed instructions');
        process.exit(1);
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test script error:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
