# Aureus Alliance Holdings Mobile App - Environment Configuration
# Copy this file to .env and fill in your actual values
# IMPORTANT: Use the EXACT SAME values as your main bot project

# App Environment
NODE_ENV=development

# Supabase Configuration (MUST MATCH main project .env)
# These values should be identical to your Telegram bot configuration
SUPABASE_URL=https://fgubaqoftdeefcakejwu.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database Connection (same as bot)
SUPABASE_DB_HOST=your_db_host
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=your_db_name
SUPABASE_DB_USER=your_db_user
SUPABASE_DB_PASSWORD=your_db_password

# Security (same JWT secret as bot for compatibility)
JWT_SECRET=your_jwt_secret_here

# Mobile App Specific Configuration
APP_NAME=Aureus Alliance Holdings
APP_VERSION=1.0.0
APP_BUNDLE_ID=com.aureusalliance.goldshares

# Deep Linking
APP_SCHEME=aureus
APP_HOST=aureus-alliance.com

# Push Notifications (Firebase)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_MESSAGING_SENDER_ID=your_sender_id

# API Configuration
API_BASE_URL=https://fgubaqoftdeefcakejwu.supabase.co
API_TIMEOUT=30000

# Feature Flags
ENABLE_BIOMETRIC_AUTH=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_OFFLINE_MODE=true

# Development/Debug Settings
DEBUG_MODE=true
LOG_LEVEL=info
ENABLE_FLIPPER=true
