/**
 * Aureus Alliance Holdings Mobile App
 * Profile Screen - User Account and Settings
 */

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {Card, Button, Avatar, List, Switch, Divider} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {colors, typography, spacing, shadows} from '../../constants/theme';

const ProfileScreen = ({navigation}) => {
  const {user, signOut} = useAuth();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const result = await signOut();
            if (!result.success) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const menuItems = [
    {
      id: 'personal_info',
      title: 'Personal Information',
      subtitle: 'Update your profile details',
      icon: 'person',
      onPress: () => {
        Alert.alert('Coming Soon', 'Profile editing will be available soon.');
      },
    },
    {
      id: 'kyc',
      title: 'KYC Verification',
      subtitle: 'Complete identity verification',
      icon: 'verified-user',
      badge: 'Pending',
      badgeColor: colors.warning,
      onPress: () => {
        Alert.alert('Coming Soon', 'KYC verification will be available soon.');
      },
    },
    {
      id: 'payment_methods',
      title: 'Payment Methods',
      subtitle: 'Manage payment options',
      icon: 'payment',
      onPress: () => {
        Alert.alert('Coming Soon', 'Payment method management will be available soon.');
      },
    },
    {
      id: 'transaction_history',
      title: 'Transaction History',
      subtitle: 'View all transactions',
      icon: 'history',
      onPress: () => {
        Alert.alert('Coming Soon', 'Transaction history will be available soon.');
      },
    },
    {
      id: 'referral_link',
      title: 'My Referral Link',
      subtitle: 'Share and earn commissions',
      icon: 'link',
      onPress: () => navigation.navigate('Commission'),
    },
    {
      id: 'security',
      title: 'Security Settings',
      subtitle: 'Password and security options',
      icon: 'security',
      onPress: () => {
        Alert.alert('Coming Soon', 'Security settings will be available soon.');
      },
    },
  ];

  const supportItems = [
    {
      id: 'help_center',
      title: 'Help Center',
      subtitle: 'FAQs and support articles',
      icon: 'help',
      onPress: () => {
        Alert.alert('Coming Soon', 'Help center will be available soon.');
      },
    },
    {
      id: 'contact_support',
      title: 'Contact Support',
      subtitle: 'Get help from our team',
      icon: 'support-agent',
      onPress: () => {
        Alert.alert('Contact Support', 'Email: <EMAIL>\nPhone: +27 123 456 789');
      },
    },
    {
      id: 'terms',
      title: 'Terms of Service',
      subtitle: 'Read our terms and conditions',
      icon: 'description',
      onPress: () => {
        Alert.alert('Coming Soon', 'Terms of service will be available soon.');
      },
    },
    {
      id: 'privacy',
      title: 'Privacy Policy',
      subtitle: 'How we protect your data',
      icon: 'privacy-tip',
      onPress: () => {
        Alert.alert('Coming Soon', 'Privacy policy will be available soon.');
      },
    },
  ];

  const renderMenuItem = (item) => (
    <TouchableOpacity key={item.id} onPress={item.onPress}>
      <List.Item
        title={item.title}
        description={item.subtitle}
        left={(props) => (
          <View style={styles.menuIcon}>
            <Icon name={item.icon} size={24} color={colors.primary} />
          </View>
        )}
        right={(props) => (
          <View style={styles.menuRight}>
            {item.badge && (
              <View style={[styles.badge, {backgroundColor: item.badgeColor}]}>
                <Text style={styles.badgeText}>{item.badge}</Text>
              </View>
            )}
            <Icon name="chevron-right" size={24} color={colors.textLight} />
          </View>
        )}
        titleStyle={styles.menuTitle}
        descriptionStyle={styles.menuSubtitle}
        style={styles.menuItem}
      />
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Profile Header */}
      <Card style={styles.profileCard}>
        <Card.Content>
          <View style={styles.profileHeader}>
            <Avatar.Text
              size={80}
              label={user?.user_metadata?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
              style={styles.avatar}
              labelStyle={styles.avatarLabel}
            />
            <View style={styles.profileInfo}>
              <Text style={styles.userName}>
                {user?.user_metadata?.full_name || 'Investor'}
              </Text>
              <Text style={styles.userEmail}>{user?.email}</Text>
              <View style={styles.membershipBadge}>
                <Icon name="stars" size={16} color={colors.secondary} />
                <Text style={styles.membershipText}>Gold Member</Text>
              </View>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Settings Section */}
      <Card style={styles.settingsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Settings</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Icon name="notifications" size={24} color={colors.primary} />
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Push Notifications</Text>
                <Text style={styles.settingSubtitle}>Receive investment updates</Text>
              </View>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              color={colors.primary}
            />
          </View>

          <Divider style={styles.settingDivider} />

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Icon name="fingerprint" size={24} color={colors.primary} />
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Biometric Login</Text>
                <Text style={styles.settingSubtitle}>Use fingerprint or face ID</Text>
              </View>
            </View>
            <Switch
              value={biometricEnabled}
              onValueChange={setBiometricEnabled}
              color={colors.primary}
            />
          </View>
        </Card.Content>
      </Card>

      {/* Account Section */}
      <Card style={styles.menuCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Account</Text>
          {menuItems.map(renderMenuItem)}
        </Card.Content>
      </Card>

      {/* Support Section */}
      <Card style={styles.menuCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Support</Text>
          {supportItems.map(renderMenuItem)}
        </Card.Content>
      </Card>

      {/* App Information */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <View style={styles.appInfo}>
            <Text style={styles.appName}>Aureus Alliance Holdings</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Sign Out Button */}
      <View style={styles.signOutContainer}>
        <Button
          mode="outlined"
          onPress={handleSignOut}
          style={styles.signOutButton}
          contentStyle={styles.signOutButtonContent}
          labelStyle={styles.signOutButtonLabel}
          icon="logout">
          Sign Out
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  profileCard: {
    marginBottom: spacing.lg,
    ...shadows.md,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.primary,
    marginRight: spacing.lg,
  },
  avatarLabel: {
    color: colors.secondary,
    fontWeight: 'bold',
    fontSize: 32,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.secondary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  membershipText: {
    fontSize: typography.fontSize.sm,
    color: colors.secondary,
    fontWeight: typography.fontWeight.medium,
    marginLeft: 4,
  },
  settingsCard: {
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingInfo: {
    marginLeft: spacing.md,
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  settingDivider: {
    marginVertical: spacing.md,
  },
  menuCard: {
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  menuItem: {
    paddingHorizontal: 0,
    paddingVertical: spacing.sm,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  menuTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  menuSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  menuRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: spacing.sm,
  },
  badgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.surface,
    fontWeight: typography.fontWeight.semibold,
  },
  infoCard: {
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  appInfo: {
    alignItems: 'center',
  },
  appName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: 4,
  },
  appVersion: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  signOutContainer: {
    marginTop: spacing.lg,
  },
  signOutButton: {
    borderColor: colors.error,
  },
  signOutButtonContent: {
    paddingVertical: spacing.sm,
  },
  signOutButtonLabel: {
    color: colors.error,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default ProfileScreen;
