/**
 * Aureus Alliance Holdings Mobile App
 * Purchase Screen - Share Purchase Flow
 */

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {Card, Button, TextInput, RadioButton, HelperText} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, typography, spacing, shadows} from '../../constants/theme';

const PurchaseScreen = ({navigation}) => {
  const {createSharePurchase, isLoading} = usePortfolio();
  
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('usdt');
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const sharePrice = 5.00; // This should come from investment_phases table
  const minAmount = 5;
  const maxAmount = 50000;

  const paymentMethods = [
    {
      id: 'usdt',
      name: 'USDT (Cryptocurrency)',
      description: 'Pay with USDT on BSC, Polygon, Tron, or Ethereum',
      icon: 'currency-bitcoin',
      color: colors.usdt,
      networks: ['BSC', 'Polygon', 'Tron', 'Ethereum'],
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      description: 'Direct bank transfer (South African Rand)',
      icon: 'account-balance',
      color: colors.primary,
      currency: 'ZAR',
    },
  ];

  const validateAmount = () => {
    const newErrors = {};
    const numAmount = parseFloat(amount);

    if (!amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(numAmount)) {
      newErrors.amount = 'Please enter a valid amount';
    } else if (numAmount < minAmount) {
      newErrors.amount = `Minimum amount is $${minAmount}`;
    } else if (numAmount > maxAmount) {
      newErrors.amount = `Maximum amount is $${maxAmount.toLocaleString()}`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateShares = () => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return 0;
    return (numAmount / sharePrice).toFixed(2);
  };

  const formatCurrency = (value, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(value || 0);
  };

  const handleAmountChange = (value) => {
    // Remove any non-numeric characters except decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return;
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }

    setAmount(cleanValue);
    
    // Clear amount error when user starts typing
    if (errors.amount) {
      setErrors(prev => ({...prev, amount: null}));
    }
  };

  const handlePurchase = async () => {
    if (!validateAmount()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const purchaseData = {
        total_amount: parseFloat(amount),
        shares_purchased: parseFloat(calculateShares()),
        share_price: sharePrice,
        payment_method: paymentMethod,
        status: 'pending',
        payment_proof_url: null, // Will be added after payment proof upload
      };

      const result = await createSharePurchase(purchaseData);

      if (result.success) {
        Alert.alert(
          'Purchase Initiated!',
          'Your share purchase has been created. Please proceed to payment.',
          [
            {
              text: 'Continue to Payment',
              onPress: () => navigation.navigate('PaymentProof', {
                purchaseId: result.data.id,
                amount: parseFloat(amount),
                paymentMethod,
                shares: parseFloat(calculateShares()),
              }),
            },
          ]
        );
      } else {
        Alert.alert(
          'Purchase Failed',
          result.error || 'Unable to create purchase. Please try again.',
          [{text: 'OK'}]
        );
      }
    } catch (error) {
      console.error('Purchase error:', error);
      Alert.alert(
        'Purchase Error',
        'An unexpected error occurred. Please try again.',
        [{text: 'OK'}]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedPaymentMethod = paymentMethods.find(pm => pm.id === paymentMethod);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Header Info */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <View style={styles.infoHeader}>
            <Icon name="info" size={24} color={colors.info} />
            <Text style={styles.infoTitle}>Share Purchase Information</Text>
          </View>
          <Text style={styles.infoText}>
            Current share price: <Text style={styles.priceText}>{formatCurrency(sharePrice)}</Text>
          </Text>
          <Text style={styles.infoText}>
            Investment range: {formatCurrency(minAmount)} - {formatCurrency(maxAmount)}
          </Text>
        </Card.Content>
      </Card>

      {/* Amount Input */}
      <Card style={styles.formCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Investment Amount</Text>
          
          <View style={styles.amountInputContainer}>
            <TextInput
              label="Amount (USD)"
              value={amount}
              onChangeText={handleAmountChange}
              mode="outlined"
              keyboardType="numeric"
              placeholder="Enter amount"
              error={!!errors.amount}
              left={<TextInput.Icon icon="attach-money" />}
              style={styles.amountInput}
              theme={{
                colors: {
                  primary: colors.primary,
                  outline: errors.amount ? colors.error : colors.border,
                },
              }}
            />
            <HelperText type="error" visible={!!errors.amount}>
              {errors.amount}
            </HelperText>
          </View>

          {amount && !errors.amount && (
            <View style={styles.calculationCard}>
              <View style={styles.calculationRow}>
                <Text style={styles.calculationLabel}>Shares to receive:</Text>
                <Text style={styles.calculationValue}>{calculateShares()}</Text>
              </View>
              <View style={styles.calculationRow}>
                <Text style={styles.calculationLabel}>Share price:</Text>
                <Text style={styles.calculationValue}>{formatCurrency(sharePrice)}</Text>
              </View>
              <View style={styles.calculationDivider} />
              <View style={styles.calculationRow}>
                <Text style={styles.calculationTotal}>Total investment:</Text>
                <Text style={styles.calculationTotal}>{formatCurrency(parseFloat(amount))}</Text>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Payment Method Selection */}
      <Card style={styles.formCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          
          <RadioButton.Group
            onValueChange={setPaymentMethod}
            value={paymentMethod}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethodCard,
                  paymentMethod === method.id && styles.selectedPaymentMethod
                ]}
                onPress={() => setPaymentMethod(method.id)}>
                <View style={styles.paymentMethodContent}>
                  <View style={styles.paymentMethodLeft}>
                    <View style={[styles.paymentMethodIcon, {backgroundColor: method.color + '20'}]}>
                      <Icon name={method.icon} size={24} color={method.color} />
                    </View>
                    <View style={styles.paymentMethodInfo}>
                      <Text style={styles.paymentMethodName}>{method.name}</Text>
                      <Text style={styles.paymentMethodDescription}>{method.description}</Text>
                      {method.networks && (
                        <View style={styles.networkTags}>
                          {method.networks.map((network) => (
                            <View key={network} style={styles.networkTag}>
                              <Text style={styles.networkTagText}>{network}</Text>
                            </View>
                          ))}
                        </View>
                      )}
                    </View>
                  </View>
                  <RadioButton value={method.id} color={colors.primary} />
                </View>
              </TouchableOpacity>
            ))}
          </RadioButton.Group>
        </Card.Content>
      </Card>

      {/* Purchase Summary */}
      {amount && !errors.amount && (
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Purchase Summary</Text>
            
            <View style={styles.summaryContent}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Investment Amount:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(parseFloat(amount))}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Shares to Receive:</Text>
                <Text style={styles.summaryValue}>{calculateShares()}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Payment Method:</Text>
                <Text style={styles.summaryValue}>{selectedPaymentMethod?.name}</Text>
              </View>
              <View style={styles.summaryDivider} />
              <View style={styles.summaryRow}>
                <Text style={styles.summaryTotal}>Total to Pay:</Text>
                <Text style={styles.summaryTotal}>{formatCurrency(parseFloat(amount))}</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Purchase Button */}
      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={handlePurchase}
          loading={isSubmitting}
          disabled={isSubmitting || !amount || !!errors.amount}
          style={styles.purchaseButton}
          contentStyle={styles.purchaseButtonContent}
          labelStyle={styles.purchaseButtonLabel}>
          {isSubmitting ? 'Processing...' : 'Proceed to Payment'}
        </Button>
      </View>

      {/* Disclaimer */}
      <View style={styles.disclaimerContainer}>
        <Icon name="info-outline" size={16} color={colors.textLight} />
        <Text style={styles.disclaimerText}>
          Your purchase will be pending until payment is confirmed by our team. 
          You will receive shares in your portfolio once payment is approved.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  infoCard: {
    marginBottom: spacing.lg,
    backgroundColor: colors.info + '10',
    ...shadows.sm,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  infoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  infoText: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  priceText: {
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary,
  },
  formCard: {
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  amountInputContainer: {
    marginBottom: spacing.md,
  },
  amountInput: {
    backgroundColor: colors.surface,
  },
  calculationCard: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: spacing.md,
    marginTop: spacing.sm,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  calculationLabel: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
  },
  calculationValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  calculationDivider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.sm,
  },
  calculationTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary,
  },
  paymentMethodCard: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  selectedPaymentMethod: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: 4,
  },
  paymentMethodDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  networkTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  networkTag: {
    backgroundColor: colors.secondary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 4,
  },
  networkTagText: {
    fontSize: typography.fontSize.xs,
    color: colors.secondary,
    fontWeight: typography.fontWeight.medium,
  },
  summaryCard: {
    marginBottom: spacing.lg,
    backgroundColor: colors.primary + '10',
    ...shadows.sm,
  },
  summaryContent: {
    gap: spacing.sm,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  summaryDivider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.sm,
  },
  summaryTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  buttonContainer: {
    marginBottom: spacing.lg,
  },
  purchaseButton: {
    backgroundColor: colors.primary,
  },
  purchaseButtonContent: {
    paddingVertical: spacing.md,
  },
  purchaseButtonLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  disclaimerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: 8,
    gap: spacing.sm,
  },
  disclaimerText: {
    fontSize: typography.fontSize.sm,
    color: colors.textLight,
    lineHeight: 20,
    flex: 1,
  },
});

export default PurchaseScreen;
