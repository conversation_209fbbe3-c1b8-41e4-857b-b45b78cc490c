const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function deployWithdrawalEnhancements() {
  try {
    console.log('🚀 [WITHDRAWAL-DEPLOY] Starting commission withdrawal system enhancements...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', '20250109_enhance_commission_withdrawals.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 [WITHDRAWAL-DEPLOY] Executing database migration...');

    // Execute the migration
    const { error: migrationError } = await supabase.rpc('exec', {
      sql: migrationSQL
    });

    if (migrationError) {
      console.error('❌ [WITHDRAWAL-DEPLOY] Migration failed:', migrationError);
      return false;
    }

    console.log('✅ [WITHDRAWAL-DEPLOY] Database migration completed successfully!');

    // Test the enhanced table structure
    console.log('🧪 [WITHDRAWAL-DEPLOY] Testing enhanced table structure...');
    
    const { data: tableInfo, error: tableError } = await supabase
      .from('commission_withdrawals')
      .select('*')
      .limit(1);

    if (tableError) {
      console.log('⚠️ [WITHDRAWAL-DEPLOY] Table test failed (expected if no withdrawals exist):', tableError.message);
    } else {
      console.log('✅ [WITHDRAWAL-DEPLOY] Table structure test passed!');
    }

    console.log('🎉 [WITHDRAWAL-DEPLOY] Commission withdrawal system enhancements deployed successfully!');
    return true;

  } catch (error) {
    console.error('💥 [WITHDRAWAL-DEPLOY] Deployment failed:', error);
    return false;
  }
}

// Run the deployment
if (require.main === module) {
  deployWithdrawalEnhancements()
    .then(success => {
      if (success) {
        console.log('✅ Deployment completed successfully');
        process.exit(0);
      } else {
        console.log('❌ Deployment failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Deployment error:', error);
      process.exit(1);
    });
}

module.exports = { deployWithdrawalEnhancements };
