// Simple migration runner for Railway deployment
// Run this once to add the missing columns to commission_withdrawals table

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client directly
const supabaseUrl = process.env.SUPABASE_URL || 'https://ixqjqfqjqjqjqjqjqjqj.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 [MIGRATION] Starting commission withdrawal table enhancement...');

    // Add missing columns one by one with error handling
    const columns = [
      {
        name: 'network',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS network VARCHAR(10);"
      },
      {
        name: 'transaction_hash',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS transaction_hash VARCHAR(255);"
      },
      {
        name: 'proof_of_payment_url',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS proof_of_payment_url VARCHAR(500);"
      },
      {
        name: 'user_confirmed_receipt',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS user_confirmed_receipt BOOLEAN DEFAULT FALSE;"
      },
      {
        name: 'confirmation_timestamp',
        sql: "ALTER TABLE commission_withdrawals ADD COLUMN IF NOT EXISTS confirmation_timestamp TIMESTAMPTZ;"
      }
    ];

    for (const column of columns) {
      console.log(`📄 [MIGRATION] Adding column: ${column.name}`);
      
      const { error } = await supabase.rpc('exec', {
        sql: column.sql
      });

      if (error) {
        console.error(`❌ [MIGRATION] Failed to add ${column.name}:`, error);
      } else {
        console.log(`✅ [MIGRATION] Added column: ${column.name}`);
      }
    }

    // Add indexes
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_network ON commission_withdrawals(network);",
      "CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_transaction_hash ON commission_withdrawals(transaction_hash);",
      "CREATE INDEX IF NOT EXISTS idx_commission_withdrawals_user_confirmed ON commission_withdrawals(user_confirmed_receipt);"
    ];

    for (const indexSql of indexes) {
      console.log(`📊 [MIGRATION] Creating index...`);
      
      const { error } = await supabase.rpc('exec', {
        sql: indexSql
      });

      if (error) {
        console.error(`❌ [MIGRATION] Index creation failed:`, error);
      } else {
        console.log(`✅ [MIGRATION] Index created successfully`);
      }
    }

    console.log('🎉 [MIGRATION] Commission withdrawal table enhancement completed!');
    return true;

  } catch (error) {
    console.error('💥 [MIGRATION] Migration failed:', error);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  runMigration()
    .then(success => {
      if (success) {
        console.log('✅ Migration completed successfully');
        process.exit(0);
      } else {
        console.log('❌ Migration failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Migration error:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
