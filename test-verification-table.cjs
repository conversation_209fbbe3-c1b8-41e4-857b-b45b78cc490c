const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function checkTable() {
  console.log('🔍 Checking email_verification_codes table...');
  
  try {
    const { data, error } = await supabase
      .from('email_verification_codes')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ Table issue:', error.message);
      console.log('🔧 Table needs to be created in Supabase dashboard');
      console.log('📋 SQL to create the table:');
      console.log(`
CREATE TABLE email_verification_codes (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  code_hash TEXT NOT NULL,
  purpose TEXT NOT NULL CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection')),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  attempts INTEGER DEFAULT 0,
  verified_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_email_verification_codes_user_id ON email_verification_codes(user_id);
CREATE INDEX idx_email_verification_codes_email ON email_verification_codes(email);
CREATE INDEX idx_email_verification_codes_purpose ON email_verification_codes(purpose);
CREATE INDEX idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
      `);
    } else {
      console.log('✅ Table exists');
      
      // Test telegram_connection purpose
      try {
        const { data: testData, error: testError } = await supabase
          .from('email_verification_codes')
          .insert({
            user_id: 106,
            email: '<EMAIL>',
            code_hash: 'test_hash_123',
            purpose: 'telegram_connection',
            expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
            attempts: 0
          })
          .select()
          .single();
        
        if (testError) {
          console.error('❌ telegram_connection purpose error:', testError.message);
        } else {
          console.log('✅ telegram_connection purpose works!');
          // Clean up
          await supabase
            .from('email_verification_codes')
            .delete()
            .eq('id', testData.id);
        }
      } catch (e) {
        console.error('❌ Test exception:', e.message);
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

checkTable();
