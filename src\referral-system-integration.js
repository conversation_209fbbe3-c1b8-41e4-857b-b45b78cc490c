// Enhanced Referral System Integration
// Integration points for enhanced referral features with existing bot
// Date: 2025-01-10

const { handleEnhancedReferralDashboard, handleDetailedCommissionHistory } = require('./referral-dashboard-enhanced');
const { handlePerformanceAnalytics, handleIndividualReferralDetails } = require('./referral-performance-analytics');
const { handleReferralPerformanceSummary } = require('./referral-performance-summary');
const { 
  handleAdminReferralManagement, 
  handleUserCommissionAudit, 
  handleReferralTracker, 
  handleCommissionVerification,
  handleBulkCommissionAdjustments,
  handleNetworkVisualization,
  handleCommissionAuditLogs
} = require('./admin-referral-management');
const { 
  handleRealTimeCommissionTracking, 
  handleCommissionReconciliation, 
  handleBulkCommissionOperations 
} = require('./admin-dashboard-features');
const { 
  commissionErrorHandler, 
  auditTrailManager, 
  processCommissionWithErrorHandling 
} = require('./error-handling-audit');

/**
 * Enhanced Referral System Integration Class
 */
class EnhancedReferralSystem {
  constructor(bot) {
    this.bot = bot;
    this.setupCallbackHandlers();
  }

  /**
   * Setup callback handlers for enhanced referral features
   */
  setupCallbackHandlers() {
    // User-facing referral dashboard callbacks
    this.bot.action('enhanced_referral_dashboard', handleEnhancedReferralDashboard);
    this.bot.action('view_detailed_commission_history', handleDetailedCommissionHistory);
    this.bot.action('view_performance_analytics', handlePerformanceAnalytics);
    this.bot.action('view_individual_referral_details', handleIndividualReferralDetails);
    this.bot.action('referral_performance_summary', handleReferralPerformanceSummary);

    // Individual referral detail callbacks
    this.bot.action(/^view_referral_detail_(\d+)$/, async (ctx) => {
      const referredId = ctx.match[1];
      await this.handleSpecificReferralDetail(ctx, referredId);
    });

    // Commission calculator callback
    this.bot.action('commission_calculator', async (ctx) => {
      await this.handleCommissionCalculator(ctx);
    });

    // Export functionality callbacks
    this.bot.action('export_commission_history', async (ctx) => {
      await this.handleExportCommissionHistory(ctx);
    });

    this.bot.action('export_analytics_report', async (ctx) => {
      await this.handleExportAnalyticsReport(ctx);
    });

    // Admin referral management callbacks
    this.bot.action('admin_referral_management', handleAdminReferralManagement);
    this.bot.action('admin_user_commission_audit', handleUserCommissionAudit);
    this.bot.action('admin_referral_tracker', handleReferralTracker);
    this.bot.action('admin_commission_verification', handleCommissionVerification);
    this.bot.action('admin_bulk_adjustments', handleBulkCommissionAdjustments);
    this.bot.action('admin_network_visualization', handleNetworkVisualization);
    this.bot.action('admin_commission_audit_logs', handleCommissionAuditLogs);

    // Admin dashboard features
    this.bot.action('realtime_commission_tracking', handleRealTimeCommissionTracking);
    this.bot.action('commission_reconciliation', handleCommissionReconciliation);
    this.bot.action('bulk_commission_operations', handleBulkCommissionOperations);

    // Optimization tips callback
    this.bot.action('referral_optimization_tips', async (ctx) => {
      await this.handleOptimizationTips(ctx);
    });
  }

  /**
   * Handle specific referral detail view
   */
  async handleSpecificReferralDetail(ctx, referredId) {
    try {
      const user = ctx.from;

      // Get user ID
      const { data: telegramUser, error: telegramError } = await db.client
        .from('telegram_users')
        .select('user_id')
        .eq('telegram_id', user.id)
        .single();

      if (telegramError || !telegramUser) {
        await ctx.replyWithMarkdown('❌ **User not found**\n\nPlease register first.');
        return;
      }

      // Get detailed information for specific referral
      const { data: referralDetail, error: detailError } = await db.client
        .from('commission_transactions')
        .select(`
          id,
          usdt_commission,
          share_commission,
          share_purchase_amount,
          payment_date,
          transaction_source,
          commission_details,
          users!commission_transactions_referred_id_fkey (
            username,
            full_name,
            created_at
          )
        `)
        .eq('referrer_id', telegramUser.user_id)
        .eq('referred_id', referredId)
        .eq('status', 'approved')
        .order('payment_date', { ascending: false });

      if (detailError || !referralDetail || referralDetail.length === 0) {
        await ctx.replyWithMarkdown('❌ **No commission data found for this referral**');
        return;
      }

      const referralUser = referralDetail[0].users;
      const totalUSDT = referralDetail.reduce((sum, t) => sum + parseFloat(t.usdt_commission || 0), 0);
      const totalShares = referralDetail.reduce((sum, t) => sum + parseFloat(t.share_commission || 0), 0);
      const totalInvestment = referralDetail.reduce((sum, t) => sum + parseFloat(t.share_purchase_amount || 0), 0);

      let detailMessage = `👤 **REFERRAL DETAIL: ${referralUser.username || referralUser.full_name || 'Anonymous'}**\n\n`;

      // Summary
      detailMessage += `📊 **SUMMARY**\n`;
      detailMessage += `• **Total USDT Commission:** $${totalUSDT.toFixed(2)}\n`;
      detailMessage += `• **Total Share Commission:** ${totalShares.toFixed(2)} shares\n`;
      detailMessage += `• **Total Investment Generated:** $${totalInvestment.toFixed(2)}\n`;
      detailMessage += `• **Number of Transactions:** ${referralDetail.length}\n`;
      detailMessage += `• **Average Commission:** $${((totalUSDT + totalShares) / referralDetail.length).toFixed(2)}\n`;
      detailMessage += `• **Member Since:** ${new Date(referralUser.created_at).toLocaleDateString()}\n\n`;

      // Recent transactions
      detailMessage += `📋 **RECENT TRANSACTIONS (Last 10)**\n\n`;
      referralDetail.slice(0, 10).forEach((transaction, index) => {
        const date = new Date(transaction.payment_date).toLocaleDateString();
        const usdtCommission = parseFloat(transaction.usdt_commission || 0);
        const shareCommission = parseFloat(transaction.share_commission || 0);
        const purchaseAmount = parseFloat(transaction.share_purchase_amount || 0);

        detailMessage += `**${index + 1}.** ${date}\n`;
        detailMessage += `   💰 USDT: $${usdtCommission.toFixed(2)} | 📈 Shares: ${shareCommission.toFixed(2)}\n`;
        detailMessage += `   🛒 Purchase: $${purchaseAmount.toFixed(2)}\n\n`;
      });

      const keyboard = [
        [
          { text: "📊 Full Transaction History", callback_data: `export_referral_history_${referredId}` },
          { text: "📈 Performance Chart", callback_data: `referral_performance_chart_${referredId}` }
        ],
        [
          { text: "🔙 Back to Referral List", callback_data: "view_individual_referral_details" }
        ]
      ];

      try {
        await ctx.replyWithMarkdown(detailMessage, {
          reply_markup: { inline_keyboard: keyboard }
        });
      } catch (markdownError) {
        console.error('❌ Markdown parsing error in referral detail:', markdownError);
        
        // Fallback: Send without markdown formatting
        const plainMessage = detailMessage.replace(/\*\*/g, '').replace(/`/g, '');
        await ctx.reply(plainMessage, {
          reply_markup: { inline_keyboard: keyboard }
        });
      }

    } catch (error) {
      console.error('Specific referral detail error:', error);
      await ctx.replyWithMarkdown('❌ **Error loading referral details**\n\nPlease try again.');
    }
  }

  /**
   * Handle commission calculator
   */
  async handleCommissionCalculator(ctx) {
    try {
      let calculatorMessage = `🎯 **COMMISSION CALCULATOR**\n\n`;
      calculatorMessage += `Calculate potential commissions based on investment amounts:\n\n`;

      // Commission calculation examples
      const investmentAmounts = [100, 500, 1000, 5000, 10000];
      const commissionRate = 15; // 15% commission rate

      calculatorMessage += `📊 **COMMISSION EXAMPLES (${commissionRate}% rate)**\n\n`;

      investmentAmounts.forEach(amount => {
        const usdtCommission = (amount * (commissionRate / 100)).toFixed(2);
        const shareCommission = (amount / 5 * (commissionRate / 100)).toFixed(2); // Assuming $5 per share
        
        calculatorMessage += `💰 **$${amount} Investment**\n`;
        calculatorMessage += `   • USDT Commission: $${usdtCommission}\n`;
        calculatorMessage += `   • Share Commission: ${shareCommission} shares\n`;
        calculatorMessage += `   • Total Value: ~$${(parseFloat(usdtCommission) + parseFloat(shareCommission) * 5).toFixed(2)}\n\n`;
      });

      calculatorMessage += `💡 **HOW IT WORKS**\n`;
      calculatorMessage += `• You earn ${commissionRate}% commission on all referral investments\n`;
      calculatorMessage += `• Commission is split between USDT and shares\n`;
      calculatorMessage += `• Shares can be converted to USDT later\n`;
      calculatorMessage += `• No limit on number of referrals\n\n`;

      calculatorMessage += `🎯 **GROWTH POTENTIAL**\n`;
      calculatorMessage += `• 10 referrals × $1,000 each = $1,500 commission\n`;
      calculatorMessage += `• 50 referrals × $500 each = $3,750 commission\n`;
      calculatorMessage += `• 100 referrals × $1,000 each = $15,000 commission\n`;

      const keyboard = [
        [
          { text: "📤 Share Referral Link", callback_data: "share_referral" },
          { text: "📊 View My Performance", callback_data: "view_performance_analytics" }
        ],
        [
          { text: "💰 Check Commission Balance", callback_data: "view_commission" },
          { text: "🔙 Back to Dashboard", callback_data: "enhanced_referral_dashboard" }
        ]
      ];

      await ctx.replyWithMarkdown(calculatorMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });

    } catch (error) {
      console.error('Commission calculator error:', error);
      await ctx.replyWithMarkdown('❌ **Error loading commission calculator**\n\nPlease try again.');
    }
  }

  /**
   * Handle export commission history
   */
  async handleExportCommissionHistory(ctx) {
    try {
      await ctx.replyWithMarkdown(`📋 **EXPORT COMMISSION HISTORY**\n\n` +
        `Your commission history export is being prepared...\n\n` +
        `📧 **Export Options:**\n` +
        `• CSV format with all transaction details\n` +
        `• PDF report with charts and summaries\n` +
        `• Excel spreadsheet with pivot tables\n\n` +
        `The export will be sent to you via direct message when ready.\n\n` +
        `⏱️ **Estimated time:** 2-3 minutes`);

      // In a real implementation, this would generate and send the export
      // For now, we'll just acknowledge the request
      setTimeout(async () => {
        try {
          await ctx.replyWithMarkdown(`✅ **Export Complete!**\n\n` +
            `Your commission history has been exported successfully.\n\n` +
            `📎 **Files generated:**\n` +
            `• Commission_History_${new Date().toISOString().split('T')[0]}.csv\n` +
            `• Commission_Report_${new Date().toISOString().split('T')[0]}.pdf\n\n` +
            `Files would be sent via direct message in a real implementation.`);
        } catch (error) {
          console.error('Export notification error:', error);
        }
      }, 3000);

    } catch (error) {
      console.error('Export commission history error:', error);
      await ctx.replyWithMarkdown('❌ **Error initiating export**\n\nPlease try again.');
    }
  }

  /**
   * Handle export analytics report
   */
  async handleExportAnalyticsReport(ctx) {
    try {
      await ctx.replyWithMarkdown(`📊 **EXPORT ANALYTICS REPORT**\n\n` +
        `Your comprehensive analytics report is being generated...\n\n` +
        `📈 **Report includes:**\n` +
        `• Performance trends and charts\n` +
        `• Referral conversion analysis\n` +
        `• Commission breakdown by period\n` +
        `• Network growth visualization\n` +
        `• Optimization recommendations\n\n` +
        `⏱️ **Estimated time:** 3-5 minutes`);

      // Simulate report generation
      setTimeout(async () => {
        try {
          await ctx.replyWithMarkdown(`✅ **Analytics Report Ready!**\n\n` +
            `Your comprehensive analytics report has been generated.\n\n` +
            `📊 **Report highlights:**\n` +
            `• 25-page detailed analysis\n` +
            `• Interactive charts and graphs\n` +
            `• Actionable insights and recommendations\n` +
            `• Benchmark comparisons\n\n` +
            `The report would be sent via direct message in a real implementation.`);
        } catch (error) {
          console.error('Analytics report notification error:', error);
        }
      }, 5000);

    } catch (error) {
      console.error('Export analytics report error:', error);
      await ctx.replyWithMarkdown('❌ **Error generating analytics report**\n\nPlease try again.');
    }
  }

  /**
   * Handle optimization tips
   */
  async handleOptimizationTips(ctx) {
    try {
      let tipsMessage = `💡 **REFERRAL OPTIMIZATION TIPS**\n\n`;

      tipsMessage += `🎯 **PROVEN STRATEGIES**\n\n`;

      tipsMessage += `**1. 📱 Social Media Approach**\n`;
      tipsMessage += `• Share your success story with screenshots\n`;
      tipsMessage += `• Post about investment opportunities\n`;
      tipsMessage += `• Use relevant hashtags and groups\n`;
      tipsMessage += `• Engage with crypto communities\n\n`;

      tipsMessage += `**2. 👥 Personal Network**\n`;
      tipsMessage += `• Start with friends and family\n`;
      tipsMessage += `• Explain the investment opportunity clearly\n`;
      tipsMessage += `• Share your own positive experience\n`;
      tipsMessage += `• Offer to help them get started\n\n`;

      tipsMessage += `**3. 📧 Content Marketing**\n`;
      tipsMessage += `• Create educational content about investing\n`;
      tipsMessage += `• Share market insights and analysis\n`;
      tipsMessage += `• Write about Aureus Alliance benefits\n`;
      tipsMessage += `• Build trust through valuable information\n\n`;

      tipsMessage += `**4. 🤝 Community Building**\n`;
      tipsMessage += `• Create a Telegram group for your referrals\n`;
      tipsMessage += `• Share updates and market news\n`;
      tipsMessage += `• Celebrate member successes\n`;
      tipsMessage += `• Provide ongoing support and guidance\n\n`;

      tipsMessage += `⚡ **QUICK WINS**\n`;
      tipsMessage += `• Update your social media bio with referral link\n`;
      tipsMessage += `• Share in relevant WhatsApp groups\n`;
      tipsMessage += `• Post in crypto forums and communities\n`;
      tipsMessage += `• Create YouTube videos about your experience\n\n`;

      tipsMessage += `📈 **TRACKING SUCCESS**\n`;
      tipsMessage += `• Monitor your conversion rates\n`;
      tipsMessage += `• Track which methods work best\n`;
      tipsMessage += `• Focus on quality over quantity\n`;
      tipsMessage += `• Follow up with potential referrals\n`;

      const keyboard = [
        [
          { text: "📤 Get My Referral Link", callback_data: "share_referral" },
          { text: "📊 View My Performance", callback_data: "view_performance_analytics" }
        ],
        [
          { text: "💰 Commission Calculator", callback_data: "commission_calculator" },
          { text: "🔙 Back to Dashboard", callback_data: "enhanced_referral_dashboard" }
        ]
      ];

      await ctx.replyWithMarkdown(tipsMessage, {
        reply_markup: { inline_keyboard: keyboard }
      });

    } catch (error) {
      console.error('Optimization tips error:', error);
      await ctx.replyWithMarkdown('❌ **Error loading optimization tips**\n\nPlease try again.');
    }
  }

  /**
   * Process commission with enhanced error handling
   */
  async processCommissionSafely(commissionData, context = {}) {
    try {
      console.log(`💰 [ENHANCED_COMMISSION] Processing commission with enhanced system`);

      // Use the enhanced commission processing with error handling
      const result = await processCommissionWithErrorHandling(commissionData, context);

      if (result.success) {
        console.log(`✅ [ENHANCED_COMMISSION] Commission processed successfully: ${result.transactionId}`);
        
        // Update referral performance summary
        await this.updateReferralPerformanceSummary(commissionData.referrer_id);
        
        return result;
      } else {
        console.error(`❌ [ENHANCED_COMMISSION] Commission processing failed:`, result);
        return result;
      }

    } catch (error) {
      console.error('Enhanced commission processing error:', error);
      return await commissionErrorHandler.handleCommissionError(error, context);
    }
  }

  /**
   * Update referral performance summary
   */
  async updateReferralPerformanceSummary(referrerId) {
    try {
      // Update daily performance summary
      await db.client.rpc('update_referral_performance_summary', {
        p_date: new Date().toISOString().split('T')[0]
      });

      console.log(`📊 [PERFORMANCE_UPDATE] Updated performance summary for referrer ${referrerId}`);

    } catch (error) {
      console.error('Performance summary update error:', error);
    }
  }

  /**
   * Get enhanced referral statistics for a user
   */
  async getEnhancedReferralStats(userId) {
    try {
      // Get comprehensive referral data using the new functions
      const [commissionBreakdown, performanceSummary, conversionRates] = await Promise.all([
        db.client.rpc('get_referral_commission_breakdown', { p_referrer_id: userId }),
        db.client.rpc('get_referral_performance_summary', { 
          p_referrer_id: userId,
          p_start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          p_end_date: new Date().toISOString().split('T')[0]
        }),
        db.client.rpc('calculate_referral_conversion_rates', { p_referrer_id: userId })
      ]);

      return {
        commissionBreakdown: commissionBreakdown.data || [],
        performanceSummary: performanceSummary.data && performanceSummary.data.length > 0 ? performanceSummary.data[0] : null,
        conversionRates: conversionRates.data || {}
      };

    } catch (error) {
      console.error('Enhanced referral stats error:', error);
      return {
        commissionBreakdown: [],
        performanceSummary: null,
        conversionRates: {}
      };
    }
  }
}

module.exports = {
  EnhancedReferralSystem
};
