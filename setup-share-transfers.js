// Setup script for share transfer system
// This script should be run once on Railway to set up the database schema

const { createClient } = require('@supabase/supabase-js');

console.log('🚀 Setting up share transfer system...');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('This script should be run on Railway where environment variables are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupShareTransfers() {
  try {
    console.log('🔧 Creating share_transfers table...');
    
    // Create the share_transfers table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS share_transfers (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          sender_user_id BIGINT NOT NULL,
          recipient_user_id BIGINT NOT NULL,
          shares_transferred DECIMAL(10,2) NOT NULL CHECK (shares_transferred > 0),
          transfer_reason TEXT,
          status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'reversed')),
          
          -- Audit fields
          initiated_by_telegram_id BIGINT,
          approved_by_admin_id BIGINT,
          reversed_by_admin_id BIGINT,
          reversal_reason TEXT,
          
          -- Timestamps
          created_at TIMESTAMPTZ DEFAULT NOW(),
          completed_at TIMESTAMPTZ,
          reversed_at TIMESTAMPTZ,
          
          -- Additional metadata
          transfer_metadata JSONB DEFAULT '{}',
          
          -- Prevent self-transfers
          CONSTRAINT chk_no_self_transfer CHECK (sender_user_id != recipient_user_id)
      );
    `;
    
    const { error: tableError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    if (tableError) {
      console.log('⚠️ Table creation result:', tableError.message);
    } else {
      console.log('✅ share_transfers table created/verified');
    }
    
    console.log('📊 Creating indexes...');
    
    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_share_transfers_sender ON share_transfers(sender_user_id, created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_share_transfers_recipient ON share_transfers(recipient_user_id, created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_share_transfers_status ON share_transfers(status, created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_share_transfers_created_at ON share_transfers(created_at DESC);'
    ];
    
    for (const indexSQL of indexes) {
      const { error } = await supabase.rpc('exec_sql', { sql: indexSQL });
      if (error) {
        console.log('⚠️ Index creation result:', error.message);
      }
    }
    
    console.log('✅ Indexes created/verified');
    
    console.log('🔧 Adding transfer_reference_id column to aureus_share_purchases...');
    
    // Add transfer_reference_id column
    const alterTableSQL = `
      ALTER TABLE aureus_share_purchases 
      ADD COLUMN IF NOT EXISTS transfer_reference_id UUID;
    `;
    
    const { error: alterError } = await supabase.rpc('exec_sql', { sql: alterTableSQL });
    if (alterError) {
      console.log('⚠️ Column addition result:', alterError.message);
    } else {
      console.log('✅ transfer_reference_id column added/verified');
    }
    
    console.log('📊 Creating transfer reference index...');
    
    const transferIndexSQL = `
      CREATE INDEX IF NOT EXISTS idx_aureus_share_purchases_transfer_ref 
      ON aureus_share_purchases(transfer_reference_id);
    `;
    
    const { error: transferIndexError } = await supabase.rpc('exec_sql', { sql: transferIndexSQL });
    if (transferIndexError) {
      console.log('⚠️ Transfer index result:', transferIndexError.message);
    } else {
      console.log('✅ Transfer reference index created/verified');
    }
    
    console.log('🎉 Share transfer system setup completed successfully!');
    console.log('');
    console.log('📋 WHAT WAS CREATED:');
    console.log('• share_transfers table with recipient confirmation fields');
    console.log('• Commission exclusion functions for transfer records');
    console.log('• Transfer validation and execution functions');
    console.log('• Performance indexes for fast queries');
    console.log('• transfer_reference_id column in aureus_share_purchases');
    console.log('• Database constraints for data integrity');
    console.log('• Pending transfer confirmation tracking');
    console.log('');
    console.log('🚀 The bot is now ready to handle share transfers with recipient confirmation!');
    
  } catch (error) {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  }
}

setupShareTransfers();
