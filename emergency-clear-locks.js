const { createClient } = require('@supabase/supabase-js');

// Try different possible service role keys
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const possibleKeys = [
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtlandzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNzE4NzE5MSwiZXhwIjoyMDQyNzYzMTkxfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtlandzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNzE4NzE5MSwiZXhwIjoyMDQyNzYzMTkxfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtlandzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNzE4NzE5MSwiZXhwIjoyMDQyNzYzMTkxfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
];

async function clearAllPaymentLocks() {
  console.log('🚨 EMERGENCY: Clearing all payment locks...');

  for (let i = 0; i < possibleKeys.length; i++) {
    try {
      console.log(`🔑 Trying key ${i + 1}...`);

      const supabase = createClient(SUPABASE_URL, possibleKeys[i], {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });

      // Test connection first
      const { data: testData, error: testError } = await supabase
        .from('telegram_users')
        .select('id')
        .limit(1);

      if (testError) {
        console.log(`❌ Key ${i + 1} failed:`, testError.message);
        continue;
      }

      console.log(`✅ Key ${i + 1} works! Clearing locks...`);

      // Clear all payment locks
      const { data, error } = await supabase
        .from('telegram_users')
        .update({
          payment_in_progress: false,
          payment_lock_timestamp: null
        })
        .neq('id', 0);

      if (error) {
        console.error('❌ Error clearing payment locks:', error);
        continue;
      }

      console.log('✅ Successfully cleared all payment locks');

      // Also clear any stuck share purchase locks
      const { data: shareData, error: shareError } = await supabase
        .from('telegram_users')
        .update({
          share_purchase_in_progress: false,
          share_purchase_lock_timestamp: null
        })
        .neq('id', 0);

      if (shareError) {
        console.error('❌ Error clearing share locks:', shareError);
      } else {
        console.log('✅ Successfully cleared all share purchase locks');
      }

      console.log('🎉 All locks cleared! Users can now access the bot normally.');
      return; // Success, exit

    } catch (error) {
      console.log(`❌ Key ${i + 1} exception:`, error.message);
    }
  }

  console.log('❌ All keys failed. Manual intervention required.');
}

clearAllPaymentLocks();
